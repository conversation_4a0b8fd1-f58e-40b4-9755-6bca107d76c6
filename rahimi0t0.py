import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import re

def sanitize_filename(name):
    return re.sub(r'[^a-zA-Z0-9_-]', '_', name)

# Step 1: Ask for URL
url = input("Enter page link: ").strip()
skip_css = input("Skip CSS? (y/n): ").strip().lower() == 'y'

# Step 2: Parse domain + path
parsed = urlparse(url)
domain = parsed.netloc
path = sanitize_filename(parsed.path.strip('/')) or "home"
folder_name = f"{domain}-{path}"
os.makedirs(folder_name, exist_ok=True)
os.makedirs(f"{folder_name}/js", exist_ok=True)

# Step 3: Download HTML
print("[+] Downloading HTML...")
res = requests.get(url)
soup = BeautifulSoup(res.text, "html.parser")

if skip_css:
    # Remove embedded <style> tags
    for style in soup.find_all("style"):
        style.decompose()
    # Remove <link rel="stylesheet"> tags
    for link in soup.find_all("link", rel="stylesheet"):
        link.decompose()

html_path = os.path.join(folder_name, "page.html")
with open(html_path, "w", encoding="utf-8") as f:
    f.write(soup.prettify())
print(f"[+] Saved HTML to {html_path}")

# Step 4: Find and download JS files
print("[+] Downloading JS files...")
scripts = soup.find_all("script", src=True)
for i, script in enumerate(scripts, start=1):
    js_url = urljoin(url, script['src'])
    js_name = js_url.split("/")[-1] or f"script_{i}.js"
    js_path = os.path.join(folder_name, "js", sanitize_filename(js_name))
    try:
        js_res = requests.get(js_url)
        with open(js_path, "wb") as f:
            f.write(js_res.content)
        print(f"[+] Saved JS: {js_url}")
    except Exception as e:
        print(f"[-] Failed to download {js_url}: {e}")

print(f"\n[✓] All files saved in: {folder_name}")
