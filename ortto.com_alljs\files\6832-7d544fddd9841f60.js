"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6832],{181:(e,t,n)=>{n.d(t,{E:()=>a});var r=n(4232);let a=n(2205).B?r.useLayoutEffect:r.useEffect},6832:(e,t,n)=>{n.d(t,{m:()=>Q});var r=n(4232),a=n(9751);let o=(0,r.createContext)({});var i=n(3866),l=n(181),u=n(4429),s=n(3160),c=n(6546),d=n(3716);function f(e){return Array.isArray(e)?e.join(" "):e}var m=n(6001),p=n(6235),g=n(2205),y=n(1200),v=n(9746);let h=1;var C=n(5048);class E extends r.Component{getSnapshotBeforeUpdate(){let{visualElement:e,props:t}=this.props;return e&&e.setProps(t),null}componentDidUpdate(){}render(){return this.props.children}}var w=n(1108);let S=Symbol.for("motionComponentSymbol");var x=n(2924),b=n(5324),A=n(2981),M=n(8574);let B=()=>({style:{},transform:{},transformKeys:[],transformOrigin:{},vars:{}});function T(e,t,n){for(let r in t)(0,A.S)(t[r])||(0,b.z)(r,n)||(e[r]=t[r])}let V=new Set(["initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","layout","layoutId","layoutDependency","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","drag","dragControls","dragListener","dragConstraints","dragDirectionLock","dragSnapToOrigin","_dragX","_dragY","dragElastic","dragMomentum","dragPropagation","dragTransition","onHoverStart","onHoverEnd","layoutScroll","whileInView","onViewportEnter","onViewportLeave","viewport","whileTap","onTap","onTapStart","onTapCancel","animate","exit","variants","whileHover","whileTap","whileFocus","whileDrag","whileInView","onPan","onPanStart","onPanSessionStart","onPanEnd"]);function D(e){return V.has(e)}let L=e=>!D(e);try{!function(e){e&&(L=t=>t.startsWith("on")?!D(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}var P=n(468);let k=()=>({...B(),attrs:{}});var H=n(7968),I=n(3202),N=n(1424),O=n(2443),R=n(1716),U=n(9163);let _=e=>(t,n)=>{let a=(0,r.useContext)(o),l=(0,r.useContext)(i.t),u=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,a,o){let i={latestValues:function(e,t,n,r){let a={},o=r(e);for(let e in o)a[e]=(0,U.u)(o[e]);let{initial:i,animate:l}=e,u=(0,d.e)(e),s=(0,d.O)(e);t&&s&&!u&&!1!==e.inherit&&(void 0===i&&(i=t.initial),void 0===l&&(l=t.animate));let c=!!n&&!1===n.initial,f=(c=c||!1===i)?l:i;return f&&"boolean"!=typeof f&&!(0,O.N)(f)&&(Array.isArray(f)?f:[f]).forEach(t=>{let n=(0,R.a)(e,t);if(!n)return;let{transitionEnd:r,transition:o,...i}=n;for(let e in i){let t=i[e];if(Array.isArray(t)){let e=c?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let e in r)a[e]=r[e]}),a}(r,a,o,e),renderState:t()};return n&&(i.mount=e=>n(r,e,i)),i})(e,t,a,l);return n?u():(0,y.M)(u)},j={useVisualState:_({scrapeMotionValuesFromProps:N.x,createRenderState:k,onMount:(e,t,{renderState:n,latestValues:r})=>{try{n.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){n.dimensions={x:0,y:0,width:0,height:0}}(0,P.B)(n,r,{enableHardwareAcceleration:!1},(0,H.n)(t.tagName),e.transformTemplate),(0,I.d)(t,n)}})},F={useVisualState:_({scrapeMotionValuesFromProps:n(863).x,createRenderState:B})},Q=function(e){function t(n,x={}){return function({preloadedFeatures:e,createVisualElement:t,projectionNodeConstructor:n,useRender:x,useVisualState:b,Component:A}){e&&(0,p.Y)(e);let M=(0,r.forwardRef)(function(p,S){var M;let B={...(0,r.useContext)(a.Q),...p,layoutId:function({layoutId:e}){let t=(0,r.useContext)(C.L).id;return t&&void 0!==e?t+"-"+e:e}(p)},{isStatic:T}=B,V=null,D=function(e){let{initial:t,animate:n}=function(e,t){if((0,d.e)(e)){let{initial:t,animate:n}=e;return{initial:!1===t||(0,c.w)(t)?t:void 0,animate:(0,c.w)(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(o));return(0,r.useMemo)(()=>({initial:t,animate:n}),[f(t),f(n)])}(p),L=T?void 0:(0,y.M)(()=>{if(v.w.hasEverUpdated)return h++}),P=b(p,T);if(!T&&g.B){D.visualElement=function(e,t,n,s){let c=(0,r.useContext)(o).visualElement,d=(0,r.useContext)(u.Y),f=(0,r.useContext)(i.t),m=(0,r.useContext)(a.Q).reducedMotion,p=(0,r.useRef)();s=s||d.renderer,!p.current&&s&&(p.current=s(e,{visualState:t,parent:c,props:n,presenceId:f?f.id:void 0,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:m}));let g=p.current;return(0,l.E)(()=>{g&&g.render()}),(0,l.E)(()=>{g&&g.animationState&&g.animationState.animateChanges()}),(0,l.E)(()=>()=>g&&g.notify("Unmount"),[]),g}(A,P,B,t);let s=(0,r.useContext)(u.Y).strict,c=(0,r.useContext)(w.N);D.visualElement&&(V=D.visualElement.loadFeatures(B,s,e,L,n||m.B.projectionNodeConstructor,c))}return r.createElement(E,{visualElement:D.visualElement,props:B},V,r.createElement(o.Provider,{value:D},x(A,p,L,(M=D.visualElement,(0,r.useCallback)(e=>{e&&P.mount&&P.mount(e),M&&(e?M.mount(e):M.unmount()),S&&("function"==typeof S?S(e):(0,s.X)(S)&&(S.current=e))},[M])),P,T,D.visualElement)))});return M[S]=A,M}(e(n,x))}if("undefined"==typeof Proxy)return t;let n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}(function(e,{forwardMotionProps:t=!1},n,a,o){return{...(0,x.Q)(e)?j:F,preloadedFeatures:n,useRender:function(e=!1){return(t,n,a,o,{latestValues:i},l)=>{let u=((0,x.Q)(t)?function(e,t,n,a){let o=(0,r.useMemo)(()=>{let n=k();return(0,P.B)(n,t,{enableHardwareAcceleration:!1},(0,H.n)(a),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};T(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t,n){let a={},o=function(e,t,n){let a=e.style||{},o={};return T(o,a,e),Object.assign(o,function({transformTemplate:e},t,n){return(0,r.useMemo)(()=>{let r=B();return(0,M.O)(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}(e,t,n)),e.transformValues?e.transformValues(o):o}(e,t,n);return e.drag&&!1!==e.dragListener&&(a.draggable=!1,o.userSelect=o.WebkitUserSelect=o.WebkitTouchCallout="none",o.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),a.style=o,a})(n,i,l,t),s={...function(e,t,n){let r={};for(let a in e)(L(a)||!0===n&&D(a)||!t&&!D(a)||e.draggable&&a.startsWith("onDrag"))&&(r[a]=e[a]);return r}(n,"string"==typeof t,e),...u,ref:o};return a&&(s["data-projection-id"]=a),(0,r.createElement)(t,s)}}(t),createVisualElement:a,projectionNodeConstructor:o,Component:e}})}}]);