=== DEBUG INFORMATION ===

1. Debug Statement
   File: script_main.KuOcpVDe.mjs.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

2. Debug Statement
   File: script_main.KuOcpVDe.mjs.js
   Line: 2
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

3. Debug Statement
   File: 2836-2e424cc73f7bf20b.js
   Line: 1
   Statement: console.error(
   Risk: LOW - Debug code should be removed from production

4. Debug Statement
   File: templates-756c36c45885c2f4.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

5. Debug Statement
   File: templates-756c36c45885c2f4.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

6. Debug Statement
   File: templates-756c36c45885c2f4.js
   Line: 9
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

7. Debug Statement
   File: blog-0c61dfa73bc0365a.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

8. Debug Statement
   File: blog-0c61dfa73bc0365a.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

9. Debug Statement
   File: blog-0c61dfa73bc0365a.js
   Line: 9
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

10. Debug Statement
   File: 9236-9454cac69276eb03.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

11. Debug Statement
   File: 9236-9454cac69276eb03.js
   Line: 1
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

12. Debug Statement
   File: 9236-9454cac69276eb03.js
   Line: 9
   Statement: console.warn(
   Risk: LOW - Debug code should be removed from production

13. Debug Statement
   File: _5B...slug_5D-3b85d30ca0b4b78b.js
   Line: 1
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

14. Debug Statement
   File: page_30.html
   Line: 42
   Statement: console.log(
   Risk: LOW - Debug code should be removed from production

15. Inline Javascript
   File: page_30.html
   Line: 35
   Content: 
   function handleSubmitCallback(data) {

        // Check if current page contains 'contact/sales/'

        if (window.location.pathname.includes('contact/sales/')) {

            console.log('Sale
   Risk: LOW - Debug code should be removed from production

16. Inline Javascript
   File: page_30.html
   Line: 85
   Content: 
   (function(w, d, s, l, i) {

      w[l] = w[l] || [];

      w[l].push({

          'gtm.start': new Date().getTime(),

          event: 'gtm.js'

      });

      var f = d.getElementsByTagName(s)
   Risk: LOW - Debug code should be removed from production

17. Inline Javascript
   File: page_30.html
   Line: 681
   Content: 
   (()=>{function u(){function n(t,e,i){let r=document.createElement("a");r.href=t,r.target=i,r.rel=e,document.body.appendChild(r),r.click(),r.remove()}function o(t){if(this.dataset.hydrated){this.re
   Risk: LOW - Debug code should be removed from production

18. Inline Javascript
   File: page_30.html
   Line: 684
   Content: 
   (()=>{function i(){for(let e of document.querySelectorAll("[data-framer-original-sizes]")){let t=e.getAttribute("data-framer-original-sizes");t===""?e.removeAttribute("sizes"):e.setAttribute("size
   Risk: LOW - Debug code should be removed from production

19. Inline Javascript
   File: page_30.html
   Line: 687
   Content: 
   !function(){function c(t,r){let e=r.indexOf("#"),n=e===-1?r:r.substring(0,e),o=e===-1?"":r.substring(e),a=n.indexOf("?");if(a===-1)return n+t+o;let d=new URLSearchParams(t),h=n.substring(a+1),s=ne
   Risk: LOW - Debug code should be removed from production

20. Inline Javascript
   File: page_30.html
   Line: 692
   Content: 
   var animator=(()=>{var k=(e,t,r)=>r>t?t:r<e?e:r;var F=()=>{};function W(e){let t;return()=>(t===void 0&&(t=e()),t)}var j=e=>e;var w=e=>e*1e3,v=e=>e/1e3;function X(e,t){return t?e*(1e3/t):0}var Y=e
   Risk: LOW - Debug code should be removed from production

21. Inline Javascript
   File: page_30.html
   Line: 695
   Content: 
   {"t98oj4":{"default":{"initial":{"opacity":0.001,"rotate":0,"rotateX":0,"rotateY":0,"scale":1,"skewX":0,"skewY":0,"transformPerspective":1200,"x":0,"y":10},"animate":{"opacity":1,"rotate":0,"rotat
   Risk: LOW - Debug code should be removed from production

22. Inline Javascript
   File: page_30.html
   Line: 698
   Content: 
   [{"hash":"3t7cke","mediaQuery":"(min-width: 1400px)"},{"hash":"1f08ijj","mediaQuery":"(min-width: 1024px) and (max-width: 1399px)"},{"hash":"q9aj3i","mediaQuery":"(min-width: 810px) and (max-width
   Risk: LOW - Debug code should be removed from production

23. Inline Javascript
   File: page_30.html
   Line: 701
   Content: 
   (()=>{function c(i,o,m){if(window.__framer_disable_appear_effects_optimization__||typeof animator>"u")return;let e={detail:{bg:document.hidden}};requestAnimationFrame(()=>{let a="framer-appear-sta
   Risk: LOW - Debug code should be removed from production

