"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2836],{2836:(e,t,n)=>{let r;function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}n.d(t,{bm:()=>e7,UC:()=>e3,VY:()=>e6,hJ:()=>e2,ZL:()=>e1,bL:()=>eQ,hE:()=>e4,l9:()=>e0});var a,u,l=n(4232),i=n.t(l,2);function c(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function s(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function d(...e){return(0,l.useCallback)(s(...e),e)}let f=(null==globalThis?void 0:globalThis.document)?l.useLayoutEffect:()=>{},p=i["useId".toString()]||(()=>void 0),m=0;function v(e){let[t,n]=l.useState(p());return f(()=>{e||n(e=>null!=e?e:String(m++))},[e]),e||(t?`radix-${t}`:"")}function h(e){let t=(0,l.useRef)(e);return(0,l.useEffect)(()=>{t.current=e}),(0,l.useMemo)(()=>(...e)=>{var n;return null==(n=t.current)?void 0:n.call(t,...e)},[])}var g=n(8477);let E=(0,l.forwardRef)((e,t)=>{let{children:n,...r}=e,a=l.Children.toArray(n),u=a.find(w);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:l.Children.count(e)>1?l.Children.only(null):(0,l.isValidElement)(e)?e.props.children:null);return(0,l.createElement)(y,o({},r,{ref:t}),(0,l.isValidElement)(e)?(0,l.cloneElement)(e,void 0,n):null)}return(0,l.createElement)(y,o({},r,{ref:t}),n)});E.displayName="Slot";let y=(0,l.forwardRef)((e,t)=>{let{children:n,...r}=e;return(0,l.isValidElement)(n)?(0,l.cloneElement)(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?s(t,n.ref):n.ref}):l.Children.count(n)>1?l.Children.only(null):null});y.displayName="SlotClone";let b=({children:e})=>(0,l.createElement)(l.Fragment,null,e);function w(e){return(0,l.isValidElement)(e)&&e.type===b}let C=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=(0,l.forwardRef)((e,n)=>{let{asChild:r,...a}=e,u=r?E:t;return(0,l.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,l.createElement)(u,o({},a,{ref:n}))});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),N="dismissableLayer.update",T=(0,l.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),R=(0,l.forwardRef)((e,t)=>{var n;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:i,onFocusOutside:s,onInteractOutside:f,onDismiss:p,...m}=e,v=(0,l.useContext)(T),[g,E]=(0,l.useState)(null),y=null!=(n=null==g?void 0:g.ownerDocument)?n:null==globalThis?void 0:globalThis.document,[,b]=(0,l.useState)({}),w=d(t,e=>E(e)),R=Array.from(v.layers),[O]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),P=R.indexOf(O),L=g?R.indexOf(g):-1,A=v.layersWithOutsidePointerEventsDisabled.size>0,M=L>=P,k=function(e,t=null==globalThis?void 0:globalThis.document){let n=h(e),r=(0,l.useRef)(!1),o=(0,l.useRef)(()=>{});return(0,l.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function a(){S("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})}"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}r.current=!1},a=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(a),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...v.branches].some(e=>e.contains(t));M&&!n&&(null==i||i(e),null==f||f(e),e.defaultPrevented||null==p||p())},y),_=function(e,t=null==globalThis?void 0:globalThis.document){let n=h(e),r=(0,l.useRef)(!1);return(0,l.useEffect)(()=>{let e=e=>{e.target&&!r.current&&S("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==s||s(e),null==f||f(e),e.defaultPrevented||null==p||p())},y);return!function(e,t=null==globalThis?void 0:globalThis.document){let n=h(e);(0,l.useEffect)(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e),()=>t.removeEventListener("keydown",e)},[n,t])}(e=>{L===v.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},y),(0,l.useEffect)(()=>{if(g)return a&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(r=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(g)),v.layers.add(g),D(),()=>{a&&1===v.layersWithOutsidePointerEventsDisabled.size&&(y.body.style.pointerEvents=r)}},[g,y,a,v]),(0,l.useEffect)(()=>()=>{g&&(v.layers.delete(g),v.layersWithOutsidePointerEventsDisabled.delete(g),D())},[g,v]),(0,l.useEffect)(()=>{let e=()=>b({});return document.addEventListener(N,e),()=>document.removeEventListener(N,e)},[]),(0,l.createElement)(C.div,o({},m,{ref:w,style:{pointerEvents:A?M?"auto":"none":void 0,...e.style},onFocusCapture:c(e.onFocusCapture,_.onFocusCapture),onBlurCapture:c(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:c(e.onPointerDownCapture,k.onPointerDownCapture)}))});function D(){let e=new CustomEvent(N);document.dispatchEvent(e)}function S(e,t,n,{discrete:r}){let o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&o.addEventListener(e,t,{once:!0}),r)o&&(0,g.flushSync)(()=>o.dispatchEvent(a));else o.dispatchEvent(a)}let O="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},A=(0,l.forwardRef)((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:u,...i}=e,[c,s]=(0,l.useState)(null),f=h(a),p=h(u),m=(0,l.useRef)(null),v=d(t,e=>s(e)),g=(0,l.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,l.useEffect)(()=>{if(r){function e(e){if(g.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:_(m.current,{select:!0})}function t(e){if(g.paused||!c)return;let t=e.relatedTarget;null!==t&&(c.contains(t)||_(m.current,{select:!0}))}document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){let t=document.activeElement;for(let n of e)n.removedNodes.length>0&&!(null!=c&&c.contains(t))&&_(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,g.paused]),(0,l.useEffect)(()=>{if(c){I.add(g);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(O,L);c.addEventListener(O,f),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(_(r,{select:t}),document.activeElement!==n)return}(M(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&_(c))}return()=>{c.removeEventListener(O,f),setTimeout(()=>{let t=new CustomEvent(P,L);c.addEventListener(P,p),c.dispatchEvent(t),t.defaultPrevented||_(null!=e?e:document.body,{select:!0}),c.removeEventListener(P,p),I.remove(g)},0)}}},[c,f,p,g]);let E=(0,l.useCallback)(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=M(e);return[k(t,e),k(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&_(a,{select:!0})):(e.preventDefault(),n&&_(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,l.createElement)(C.div,o({tabIndex:-1},i,{ref:v,onKeyDown:E}))});function M(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function k(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function _(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}let I=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=x(e,t)).unshift(t)},remove(t){var n;null==(n=(e=x(e,t))[0])||n.resume()}}}();function x(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}let F=(0,l.forwardRef)((e,t)=>{var n;let{container:r=null==globalThis||null==(n=globalThis.document)?void 0:n.body,...a}=e;return r?g.createPortal((0,l.createElement)(C.div,o({},a,{ref:t})),r):null}),W=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=(0,l.useState)(),a=(0,l.useRef)({}),u=(0,l.useRef)(e),i=(0,l.useRef)("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},(0,l.useReducer)((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return(0,l.useEffect)(()=>{let e=U(a.current);i.current="mounted"===c?e:"none"},[c]),f(()=>{let t=a.current,n=u.current;if(n!==e){let r=i.current,o=U(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),f(()=>{if(r){let e=e=>{let t=U(a.current).includes(e.animationName);e.target===r&&t&&(0,g.flushSync)(()=>s("ANIMATION_END"))},t=e=>{e.target===r&&(i.current=U(a.current))};return r.addEventListener("animationstart",t),r.addEventListener("animationcancel",e),r.addEventListener("animationend",e),()=>{r.removeEventListener("animationstart",t),r.removeEventListener("animationcancel",e),r.removeEventListener("animationend",e)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:(0,l.useCallback)(e=>{e&&(a.current=getComputedStyle(e)),o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):l.Children.only(n),a=d(r.ref,o.ref);return"function"==typeof n||r.isPresent?(0,l.cloneElement)(o,{ref:a}):null};function U(e){return(null==e?void 0:e.animationName)||"none"}W.displayName="Presence";let B=0;function j(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var K=n(2476),$="right-scroll-bar-position",X="width-before-scroll-bar";function Y(e){return e}var Z=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=Y),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},u=function(){return Promise.resolve().then(a)};u(),r={push:function(e){t.push(e),u()},filter:function(e){return t=t.filter(e),r}}}});return a.options=(0,K.Cl)({async:!0,ssr:!1},e),a}(),z=function(){},V=l.forwardRef(function(e,t){var n,r,o,a=l.useRef(null),u=l.useState({onScrollCapture:z,onWheelCapture:z,onTouchMoveCapture:z}),i=u[0],c=u[1],s=e.forwardProps,d=e.children,f=e.className,p=e.removeScrollBar,m=e.enabled,v=e.shards,h=e.sideCar,g=e.noIsolation,E=e.inert,y=e.allowPinchZoom,b=e.as,w=(0,K.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),C=(n=[a,t],r=function(e){return n.forEach(function(t){return"function"==typeof t?t(e):t&&(t.current=e),t})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,o.facade),N=(0,K.Cl)((0,K.Cl)({},w),i);return l.createElement(l.Fragment,null,m&&l.createElement(h,{sideCar:Z,removeScrollBar:p,shards:v,noIsolation:g,inert:E,setCallbacks:c,allowPinchZoom:!!y,lockRef:a}),s?l.cloneElement(l.Children.only(d),(0,K.Cl)((0,K.Cl)({},N),{ref:C})):l.createElement(void 0===b?"div":b,(0,K.Cl)({},N,{className:f,ref:C}),d))});V.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},V.classNames={fullWidth:X,zeroRight:$};var H=function(e){var t=e.sideCar,n=(0,K.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,(0,K.Cl)({},n))};H.isSideCarExport=!0;var q=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=u||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,a;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},J=function(){var e=q();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},G=function(){var e=J();return function(t){return e(t.styles,t.dynamic),null}},Q={left:0,top:0,right:0,gap:0},ee=function(e){return parseInt(e||"",10)||0},et=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ee(n),ee(r),ee(o)]},en=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Q;var t=et(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},er=G(),eo=function(e,t,n,r){var o=e.left,a=e.top,u=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat($," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(X," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat($," .").concat($," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(X," .").concat(X," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},ea=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r,a=l.useMemo(function(){return en(o)},[o]);return l.createElement(er,{styles:eo(a,!t,o,n?"":"!important")})},eu=!1;if("undefined"!=typeof window)try{var el=Object.defineProperty({},"passive",{get:function(){return eu=!0,!0}});window.addEventListener("test",el,el),window.removeEventListener("test",el,el)}catch(e){eu=!1}var ei=!!eu&&{passive:!1},ec=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},es=function(e,t){var n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),ed(e,n)){var r=ef(e,n);if(r[1]>r[2])return!0}n=n.parentNode}while(n&&n!==document.body);return!1},ed=function(e,t){return"v"===e?ec(t,"overflowY"):ec(t,"overflowX")},ef=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ep=function(e,t,n,r,o){var a,u=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=u*r,i=n.target,c=t.contains(i),s=!1,d=l>0,f=0,p=0;do{var m=ef(e,i),v=m[0],h=m[1]-m[2]-u*v;(v||h)&&ed(e,i)&&(f+=h,p+=v),i=i.parentNode}while(!c&&i!==document.body||c&&(t.contains(i)||t===i));return d&&(o&&0===f||!o&&l>f)?s=!0:!d&&(o&&0===p||!o&&-l>p)&&(s=!0),s},em=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ev=function(e){return[e.deltaX,e.deltaY]},eh=function(e){return e&&"current"in e?e.current:e},eg=0,eE=[];let ey=(a=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(eg++)[0],a=l.useState(function(){return G()})[0],u=l.useRef(e);l.useEffect(function(){u.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,K.fX)([e.lockRef.current],(e.shards||[]).map(eh),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!u.current.allowPinchZoom;var o,a=em(e),l=n.current,i="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],s=e.target,d=Math.abs(i)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=es(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=es(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(i||c)&&(r.current=o),!o)return!0;var p=r.current||o;return ep(p,t,e,"h"===p?i:c,!0)},[]),c=l.useCallback(function(e){if(eE.length&&eE[eE.length-1]===a){var n="deltaY"in e?ev(e):em(e),r=t.current.filter(function(t){var r;return t.name===e.type&&t.target===e.target&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eh).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?i(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=l.useCallback(function(e){n.current=em(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,ev(t),t.target,i(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,em(t),t.target,i(t,e.lockRef.current))},[]);l.useEffect(function(){return eE.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ei),document.addEventListener("touchmove",c,ei),document.addEventListener("touchstart",d,ei),function(){eE=eE.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,ei),document.removeEventListener("touchmove",c,ei),document.removeEventListener("touchstart",d,ei)}},[]);var m=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(ea,{gapMode:"margin"}):null)},Z.useMedium(a),H);var eb=l.forwardRef(function(e,t){return l.createElement(V,(0,K.Cl)({},e,{ref:t,sideCar:ey}))});eb.classNames=V.classNames;var ew=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},eC=new WeakMap,eN=new WeakMap,eT={},eR=0,eD=function(e){return e&&(e.host||eD(e.parentNode))},eS=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eD(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eT[n]||(eT[n]=new WeakMap);var a=eT[n],u=[],l=new Set,i=new Set(o),c=function(e){!e||l.has(e)||(l.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||i.has(e)||Array.prototype.forEach.call(e.children,function(e){if(l.has(e))s(e);else{var t=e.getAttribute(r),o=null!==t&&"false"!==t,i=(eC.get(e)||0)+1,c=(a.get(e)||0)+1;eC.set(e,i),a.set(e,c),u.push(e),1===i&&o&&eN.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}})};return s(t),l.clear(),eR++,function(){u.forEach(function(e){var t=eC.get(e)-1,o=a.get(e)-1;eC.set(e,t),a.set(e,o),t||(eN.has(e)||e.removeAttribute(r),eN.delete(e)),o||e.removeAttribute(n)}),--eR||(eC=new WeakMap,eC=new WeakMap,eN=new WeakMap,eT={})}},eO=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ew(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),eS(r,o,n,"aria-hidden")):function(){return null}};let eP="Dialog",[eL,eA]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>(0,l.createContext)(e));return function(n){let r=(null==n?void 0:n[e])||t;return(0,l.useMemo)(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=(0,l.createContext)(r),a=n.length;function u(t){let{scope:n,children:r,...u}=t,i=(null==n?void 0:n[e][a])||o,c=(0,l.useMemo)(()=>u,Object.values(u));return(0,l.createElement)(i.Provider,{value:c},r)}return n=[...n,r],u.displayName=t+"Provider",[u,function(n,u){let i=(null==u?void 0:u[e][a])||o,c=(0,l.useContext)(i);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return(0,l.useMemo)(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(eP),[eM,ek]=eL(eP),e_=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,a=ek("DialogTrigger",n),u=d(t,a.triggerRef);return(0,l.createElement)(C.button,o({type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":eq(a.open)},r,{ref:u,onClick:c(e.onClick,a.onOpenToggle)}))}),eI="DialogPortal",[ex,eF]=eL(eI,{forceMount:void 0}),eW="DialogOverlay",eU=(0,l.forwardRef)((e,t)=>{let n=eF(eW,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,u=ek(eW,e.__scopeDialog);return u.modal?(0,l.createElement)(W,{present:r||u.open},(0,l.createElement)(eB,o({},a,{ref:t}))):null}),eB=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,a=ek(eW,n);return(0,l.createElement)(eb,{as:E,allowPinchZoom:!0,shards:[a.contentRef]},(0,l.createElement)(C.div,o({"data-state":eq(a.open)},r,{ref:t,style:{pointerEvents:"auto",...r.style}})))}),ej="DialogContent",eK=(0,l.forwardRef)((e,t)=>{let n=eF(ej,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,u=ek(ej,e.__scopeDialog);return(0,l.createElement)(W,{present:r||u.open},u.modal?(0,l.createElement)(e$,o({},a,{ref:t})):(0,l.createElement)(eX,o({},a,{ref:t})))}),e$=(0,l.forwardRef)((e,t)=>{let n=ek(ej,e.__scopeDialog),r=(0,l.useRef)(null),a=d(t,n.contentRef,r);return(0,l.useEffect)(()=>{let e=r.current;if(e)return eO(e)},[]),(0,l.createElement)(eY,o({},e,{ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:c(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:c(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:c(e.onFocusOutside,e=>e.preventDefault())}))}),eX=(0,l.forwardRef)((e,t)=>{let n=ek(ej,e.__scopeDialog),r=(0,l.useRef)(!1),a=(0,l.useRef)(!1);return(0,l.createElement)(eY,o({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,u;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(r.current||null==(u=n.triggerRef.current)||u.focus(),t.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:t=>{var o,u;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let l=t.target;(null==(u=n.triggerRef.current)?void 0:u.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}}))}),eY=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:u,...i}=e,c=ek(ej,n),s=d(t,(0,l.useRef)(null));return(0,l.useEffect)(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:j()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:j()),B++,()=>{1===B&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),B--}},[]),(0,l.createElement)(l.Fragment,null,(0,l.createElement)(A,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:u},(0,l.createElement)(R,o({role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":eq(c.open)},i,{ref:s,onDismiss:()=>c.onOpenChange(!1)}))),!1)}),eZ="DialogTitle",ez=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,a=ek(eZ,n);return(0,l.createElement)(C.h2,o({id:a.titleId},r,{ref:t}))}),eV=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,a=ek("DialogDescription",n);return(0,l.createElement)(C.p,o({id:a.descriptionId},r,{ref:t}))}),eH=(0,l.forwardRef)((e,t)=>{let{__scopeDialog:n,...r}=e,a=ek("DialogClose",n);return(0,l.createElement)(C.button,o({type:"button"},r,{ref:t,onClick:c(e.onClick,()=>a.onOpenChange(!1))}))});function eq(e){return e?"open":"closed"}let[eJ,eG]=function(e,t){let n=(0,l.createContext)(t);function r(e){let{children:t,...r}=e,o=(0,l.useMemo)(()=>r,Object.values(r));return(0,l.createElement)(n.Provider,{value:o},t)}return r.displayName=e+"Provider",[r,function(r){let o=(0,l.useContext)(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}("DialogTitleWarning",{contentName:ej,titleName:eZ,docsSlug:"dialog"}),eQ=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,i=(0,l.useRef)(null),c=(0,l.useRef)(null),[s=!1,d]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,o]=function({defaultProp:e,onChange:t}){let n=(0,l.useState)(e),[r]=n,o=(0,l.useRef)(r),a=h(t);return(0,l.useEffect)(()=>{o.current!==r&&(a(r),o.current=r)},[r,o,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,u=a?e:r,i=h(n);return[u,(0,l.useCallback)(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&i(n)}else o(t)},[a,e,o,i])]}({prop:r,defaultProp:o,onChange:a});return(0,l.createElement)(eM,{scope:t,triggerRef:i,contentRef:c,contentId:v(),titleId:v(),descriptionId:v(),open:s,onOpenChange:d,onOpenToggle:(0,l.useCallback)(()=>d(e=>!e),[d]),modal:u},n)},e0=e_,e1=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=ek(eI,t);return(0,l.createElement)(ex,{scope:t,forceMount:n},l.Children.map(r,e=>(0,l.createElement)(W,{present:n||a.open},(0,l.createElement)(F,{asChild:!0,container:o},e))))},e2=eU,e3=eK,e4=ez,e6=eV,e7=eH}}]);