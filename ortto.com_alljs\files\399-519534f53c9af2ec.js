"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[399],{253:(t,e,r)=>{r.d(e,{Fl:()=>s,Kg:()=>i,SY:()=>a,aj:()=>n,ne:()=>o});let n=t=>Math.round(1e5*t)/1e5,a=/(-)?([\d]*\.?[\d])+/g,o=/(#[0-9a-f]{6}|#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,s=/^(#[0-9a-f]{3}|#(?:[0-9a-f]{2}){2,4}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function i(t){return"string"==typeof t}},468:(t,e,r)=>{r.d(e,{B:()=>l});var n=r(8574),a=r(1300);function o(t,e,r){return"string"==typeof t?t:a.px.transform(e+r*t)}let s={offset:"stroke-dashoffset",array:"stroke-dasharray"},i={offset:"strokeDashoffset",array:"strokeDasharray"};function l(t,{attrX:e,attrY:r,originX:l,originY:d,pathLength:f,pathSpacing:p=1,pathOffset:u=0,...c},h,x,m){if((0,n.O)(t,c,h,m),x){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:g,style:v,dimensions:y}=t;g.transform&&(y&&(v.transform=g.transform),delete g.transform),y&&(void 0!==l||void 0!==d||v.transform)&&(v.transformOrigin=function(t,e,r){let n=o(e,t.x,t.width),a=o(r,t.y,t.height);return`${n} ${a}`}(y,void 0!==l?l:.5,void 0!==d?d:.5)),void 0!==e&&(g.x=e),void 0!==r&&(g.y=r),void 0!==f&&function(t,e,r=1,n=0,o=!0){t.pathLength=1;let l=o?s:i;t[l.offset]=a.px.transform(-n);let d=a.px.transform(e),f=a.px.transform(r);t[l.array]=`${d} ${f}`}(g,f,p,u,!1)}},597:(t,e,r)=>{r.d(e,{Y:()=>n});function n(t){return t.startsWith("--")}},863:(t,e,r)=>{r.d(e,{x:()=>o});var n=r(5324),a=r(2981);function o(t){let{style:e}=t,r={};for(let o in e)((0,a.S)(e[o])||(0,n.z)(o,t))&&(r[o]=e[o]);return r}},981:(t,e,r)=>{r.d(e,{U:()=>n,f:()=>a});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],a=new Set(n)},1108:(t,e,r)=>{r.d(e,{N:()=>n});let n=(0,r(4232).createContext)({})},1200:(t,e,r)=>{r.d(e,{M:()=>a});var n=r(4232);function a(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},1300:(t,e,r)=>{r.d(e,{KN:()=>s,gQ:()=>f,px:()=>i,uj:()=>o,vh:()=>l,vw:()=>d});var n=r(253);let a=t=>({test:e=>(0,n.Kg)(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),o=a("deg"),s=a("%"),i=a("px"),l=a("vh"),d=a("vw"),f={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},1424:(t,e,r)=>{r.d(e,{x:()=>o});var n=r(2981),a=r(863);function o(t){let e=(0,a.x)(t);for(let r in t)(0,n.S)(t[r])&&(e["x"===r||"y"===r?"attr"+r.toUpperCase():r]=t[r]);return e}},1716:(t,e,r)=>{r.d(e,{a:()=>n});function n(t,e,r,a={},o={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,a,o)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,a,o)),e}},2205:(t,e,r)=>{r.d(e,{B:()=>n});let n="undefined"!=typeof document},2443:(t,e,r)=>{r.d(e,{N:()=>n});function n(t){return"object"==typeof t&&"function"==typeof t.start}},2924:(t,e,r)=>{r.d(e,{Q:()=>a});let n=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function a(t){if("string"!=typeof t||t.includes("-"));else if(n.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}},2981:(t,e,r)=>{r.d(e,{S:()=>n});let n=t=>!!(null==t?void 0:t.getVelocity)},3160:(t,e,r)=>{r.d(e,{X:()=>n});function n(t){return"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}},3202:(t,e,r)=>{r.d(e,{d:()=>s});var n=r(9343),a=r(6721),o=r(9858);function s(t,e,r,s){for(let r in(0,a.e)(t,e,void 0,s),e.attrs)t.setAttribute(o.e.has(r)?r:(0,n.I)(r),e.attrs[r])}},3213:(t,e,r)=>{r.d(e,{p:()=>n});let n=t=>Array.isArray(t)},3284:(t,e,r)=>{r.d(e,{B:()=>a,K:()=>o});var n=r(3213);let a=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),o=t=>(0,n.p)(t)?t[t.length-1]||0:t},3716:(t,e,r)=>{r.d(e,{O:()=>i,e:()=>s});var n=r(2443),a=r(6546);let o=["initial","animate","exit","whileHover","whileDrag","whileTap","whileFocus","whileInView"];function s(t){return(0,n.N)(t.animate)||o.some(e=>(0,a.w)(t[e]))}function i(t){return!!(s(t)||t.variants)}},3866:(t,e,r)=>{r.d(e,{t:()=>n});let n=(0,r(4232).createContext)(null)},4349:(t,e,r)=>{r.d(e,{q:()=>n});let n=(t,e,r)=>Math.min(Math.max(r,t),e)},4785:(t,e,r)=>{r.d(e,{W:()=>s});var n=r(9037),a=r(1300);let o={...n.ai,transform:Math.round},s={borderWidth:a.px,borderTopWidth:a.px,borderRightWidth:a.px,borderBottomWidth:a.px,borderLeftWidth:a.px,borderRadius:a.px,radius:a.px,borderTopLeftRadius:a.px,borderTopRightRadius:a.px,borderBottomRightRadius:a.px,borderBottomLeftRadius:a.px,width:a.px,maxWidth:a.px,height:a.px,maxHeight:a.px,size:a.px,top:a.px,right:a.px,bottom:a.px,left:a.px,padding:a.px,paddingTop:a.px,paddingRight:a.px,paddingBottom:a.px,paddingLeft:a.px,margin:a.px,marginTop:a.px,marginRight:a.px,marginBottom:a.px,marginLeft:a.px,rotate:a.uj,rotateX:a.uj,rotateY:a.uj,rotateZ:a.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:a.uj,skewX:a.uj,skewY:a.uj,distance:a.px,translateX:a.px,translateY:a.px,translateZ:a.px,x:a.px,y:a.px,z:a.px,perspective:a.px,transformPerspective:a.px,opacity:n.X4,originX:a.gQ,originY:a.gQ,originZ:a.px,zIndex:o,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:o}},4828:(t,e,r)=>{r.d(e,{u:()=>n});let n={delta:0,timestamp:0}},5048:(t,e,r)=>{r.d(e,{L:()=>n});let n=(0,r(4232).createContext)({})},5324:(t,e,r)=>{r.d(e,{z:()=>o});var n=r(7188),a=r(981);function o(t,{layout:e,layoutId:r}){return a.f.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!n.H[t]||"opacity"===t)}},5538:(t,e,r)=>{r.d(e,{eO:()=>c,qX:()=>h,OH:()=>u});let n=1/60*1e3,a="undefined"!=typeof performance?()=>performance.now():()=>Date.now(),o="undefined"!=typeof window?t=>window.requestAnimationFrame(t):t=>setTimeout(()=>t(a()),n);var s=r(4828);let i=!0,l=!1,d=!1,f=["read","update","preRender","render","postRender"],p=f.reduce((t,e)=>(t[e]=function(t){let e=[],r=[],n=0,a=!1,o=!1,s=new WeakSet,i={schedule:(t,o=!1,i=!1)=>{let l=i&&a,d=l?e:r;return o&&s.add(t),-1===d.indexOf(t)&&(d.push(t),l&&a&&(n=e.length)),t},cancel:t=>{let e=r.indexOf(t);-1!==e&&r.splice(e,1),s.delete(t)},process:l=>{if(a){o=!0;return}if(a=!0,[e,r]=[r,e],r.length=0,n=e.length)for(let r=0;r<n;r++){let n=e[r];n(l),s.has(n)&&(i.schedule(n),t())}a=!1,o&&(o=!1,i.process(l))}};return i}(()=>l=!0),t),{}),u=f.reduce((t,e)=>{let r=p[e];return t[e]=(t,e=!1,n=!1)=>(l||g(),r.schedule(t,e,n)),t},{}),c=f.reduce((t,e)=>(t[e]=p[e].cancel,t),{}),h=f.reduce((t,e)=>(t[e]=()=>p[e].process(s.u),t),{}),x=t=>p[t].process(s.u),m=t=>{l=!1,s.u.delta=i?n:Math.max(Math.min(t-s.u.timestamp,40),1),s.u.timestamp=t,d=!0,f.forEach(x),d=!1,l&&(i=!1,o(m))},g=()=>{l=!0,i=!0,d||o(m)}},6546:(t,e,r)=>{r.d(e,{w:()=>n});function n(t){return"string"==typeof t||Array.isArray(t)}},6721:(t,e,r)=>{r.d(e,{e:()=>n});function n(t,{style:e,vars:r},n,a){for(let o in Object.assign(t.style,e,a&&a.getProjectionStyles(n)),r)t.style.setProperty(o,r[o])}},7188:(t,e,r)=>{r.d(e,{$:()=>a,H:()=>n});let n={};function a(t){Object.assign(n,t)}},7968:(t,e,r)=>{r.d(e,{n:()=>n});let n=t=>"string"==typeof t&&"svg"===t.toLowerCase()},8574:(t,e,r)=>{r.d(e,{O:()=>d});var n=r(981);let a={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},o=(t,e)=>n.U.indexOf(t)-n.U.indexOf(e);var s=r(597);let i=(t,e)=>e&&"number"==typeof t?e.transform(t):t;var l=r(4785);function d(t,e,r,d){let{style:f,vars:p,transform:u,transformKeys:c,transformOrigin:h}=t;c.length=0;let x=!1,m=!1,g=!0;for(let t in e){let r=e[t];if((0,s.Y)(t)){p[t]=r;continue}let a=l.W[t],o=i(r,a);if(n.f.has(t)){if(x=!0,u[t]=o,c.push(t),!g)continue;r!==(a.default||0)&&(g=!1)}else t.startsWith("origin")?(m=!0,h[t]=o):f[t]=o}if(!e.transform&&(x||d?f.transform=function({transform:t,transformKeys:e},{enableHardwareAcceleration:r=!0,allowTransformNone:n=!0},s,i){let l="";for(let r of(e.sort(o),e))l+=`${a[r]||r}(${t[r]}) `;return r&&!t.z&&(l+="translateZ(0)"),l=l.trim(),i?l=i(t,s?"":l):n&&s&&(l="none"),l}(t,r,g,d):f.transform&&(f.transform="none")),m){let{originX:t="50%",originY:e="50%",originZ:r=0}=h;f.transformOrigin=`${t} ${e} ${r}`}}},9037:(t,e,r)=>{r.d(e,{X4:()=>o,ai:()=>a,hs:()=>s});var n=r(4349);let a={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},o={...a,transform:t=>(0,n.q)(0,1,t)},s={...a,default:1}},9163:(t,e,r)=>{r.d(e,{u:()=>o});var n=r(3284),a=r(2981);function o(t){let e=(0,a.S)(t)?t.get():t;return(0,n.B)(e)?e.toValue():e}},9343:(t,e,r)=>{r.d(e,{I:()=>n});let n=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()},9746:(t,e,r)=>{r.d(e,{w:()=>n});let n={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},9751:(t,e,r)=>{r.d(e,{Q:()=>n});let n=(0,r(4232).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},9858:(t,e,r)=>{r.d(e,{e:()=>n});let n=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"])}}]);