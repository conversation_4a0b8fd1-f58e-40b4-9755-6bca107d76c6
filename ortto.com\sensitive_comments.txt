=== SENSITIVE COMMENTS ===

1. Sensitive Comment
   File: script_v_2.js
   Line: 1
   Comment: //framerusercontent.com/sites/"}}return Te}function x(){return Math.floor((1+Math.random())*65536).toString(16).substring(1)}function mt(){return`${x()}${x()}-${x()}-${x()}-${x()}-${x()}${x()}${x()}`}var E=class{constructor(e,i){this.event=e;this.data=i}serialize(){return{source:"framer.site",timestamp:Date.now(),data:{type:"track",uuid:mt(),event:this.event,...this.data}}}};var Re=new Set,ve=t=>Re.forEach(({callback:e,on:i})=>i===t&&e()),R=(t,e="lazy")=>Re.add({callback:t,on:e});addEventListener("visibilitychange",()=>{document.hidden&&ve("lazy")});addEventListener("pagehide",()=>ve("lazy"));addEventListener("load",()=>ve("load"));var gt="fetchLater"in window,G,te,O;function ft(t,e,i,r){let o=JSON.stringify(e);try{return window.fetchLater(t,{method:"POST",body:o,signal:r,activateAfter:i?3e3:10*6e4})}catch{return Ie(t,o),{activated:!0}}}function pt(t,e,i){return gt?(te&&te.abort(),G!=null&&G.activated&&O&&(O.length=0),O??=[],O=O.concat(e),te=new AbortController,G=ft(t,O,i,te.signal),G):!1}function Ie(t,e){fetch(t,{method:"POST",body:e})}function ht(t,e){return navigator.sendBeacon(t,e)}function Be(t,e,i=!1){if(!pt(t,e,i)){let r=JSON.stringify(e);ht(t,r)||Ie(t,r)}}var ne=new Set;function _e(){for(let t of ne)t();ne.clear()}var N=window.scheduler,Tt=N&&"yield"in N,vt=N&&"postTask"in N;function I(t=!1){return new Promise(e=>{if(ne.add(e),!document.hidden){requestAnimationFrame(async()=>{let i=()=>{ne.delete(e),e()};t?Tt?(await N.yield(),i()):vt?N.postTask(i):i():setTimeout(i,1)});return}_e()})}R(_e,"lazy");var Oe=L(),yt=new URL(Oe.src),St=`${yt.origin}/anonymous`;function Ne(t,e=!1){if(!location.protocol.startsWith("http"))return;let i={framerSiteId:Oe.framerSiteId,origin:document.location.origin,pathname:document.location.pathname,search:document.location.search,visitTimeOrigin:performance.timeOrigin};Be(St,t.map(r=>({...r,data:{...r.data,context:{...i,...r.data.context}}})),e)}function We(t,e){return t==="eager"||e==="eager"?"eager":e??t}var re=new Set,ye=!1;function Se(){if(re.size===0)return;if(!ye){ye=!0,queueMicrotask(Se);return}let t=[];re.forEach(e=>e.forEach(i=>t.push(i.serialize()))),re.clear(),Ne(t),ye=!1}async function k(t,e="lazy"){if(t.length!==0){if(e==="eager"){await I(),Ne(t.map(i=>i.serialize()),!0);return}re.add(t),document.hidden&&Se()}}R(Se,"lazy");var W="__framer_events";function Ue(){window[W]||(window[W]=[]);function t(e){let i,r=e.map(o=>{let[a,n,s]=o;return i=We(i,s),new E(a,n)});k(r,i??"eager")}window[W].length>0&&(t(window[W]),window[W].length=0),window[W].push=(...e)=>(t(e),-1)}var Et=L();function Z(t){let e=[new E("published_site_pageview",{referrer:(t==null?void 0:t.initialReferrer)||null,url:location.href,hostname:location.hostname||null,pathname:location.pathname||null,hash:location.hash||null,search:location.search||null,framerSiteId:Et.framerSiteId,timezone:V,locale:he})];k(e,"eager")}function $e(){addEventListener("popstate",()=>Z());let t=history.pushState;history.pushState=(...e)=>{t.apply(history,e),Z()}}var oe=class{t;o=0;i=[];u(e){var o;if(e.hadRecentInput)return;let i=this.i[0],r=this.i.at(-1);this.o&&i&&r&&e.startTime-r.startTime<1e3&&e.startTime-i.startTime<5e3?(this.o+=e.value,this.i.push(e)):(this.o=e.value,this.i=[e]),(o=this.t)==null||o.call(this,e)}},H=()=>{let t=performance.getEntriesByType("navigation")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},ae=t=>{if(document.readyState==="loading")return"loading";{let e=H();if(e){if(t<e.domInteractive)return"loading";if(e.domContentLoadedEventStart===0||t<e.domContentLoadedEventStart)return"dom-interactive";if(e.domComplete===0||t<e.domComplete)return"dom-content-loaded"}}return"complete"},bt=t=>{let e=t.nodeName;return t.nodeType===1?e.toLowerCase():e.toUpperCase().replace(/^#/,"")},Le=t=>{let e="";try{for(;(t==null?void 0:t.nodeType)!==9;){let i=t,r=i.id?"#"+i.id:[bt(i),...Array.from(i.classList).sort()].join(".");if(e.length+r.length>99)return e||r;if(e=e?r+">"+e:r,i.id)break;t=i.parentNode}}catch{}return e},Ee=new WeakMap;function $(t,e){return Ee.get(t)||Ee.set(t,new e),Ee.get(t)}var Ke=-1,et=()=>Ke,j=t=>{addEventListener("pageshow",e=>{e.persisted&&(Ke=e.timeStamp,t(e))},!0)},C=(t,e,i,r)=>{let o,a;return n=>{e.value>=0&&(n||r)&&(a=e.value-(o??0),(a||o===void 0)&&(o=e.value,e.delta=a,e.rating=((s,l)=>s>l[1]?"poor":s>l[0]?"needs-improvement":"good")(e.value,i),t(e)))}},ke=t=>{requestAnimationFrame(()=>requestAnimationFrame(()=>t()))},X=()=>{let t=H();return(t==null?void 0:t.activationStart)??0},P=(t,e=-1)=>{let i=H(),r="navigate";return et()>=0?r="back-forward-cache":i&&(document.prerendering||X()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:t,value:e,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},q=(t,e,i={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){let r=new PerformanceObserver(o=>{Promise.resolve().then(()=>{e(o.getEntries())})});return r.observe({type:t,buffered:!0,...i}),r}}catch{}},Me=t=>{let e=!1;return()=>{e||(t(),e=!0)}},U=-1,qe=()=>document.visibilityState!=="hidden"||document.prerendering?1/0:0,se=t=>{document.visibilityState==="hidden"&&U>-1&&(U=t.type==="visibilitychange"?t.timeStamp:0,Ct())},He=()=>{addEventListener("visibilitychange",se,!0),addEventListener("prerenderingchange",se,!0)},Ct=()=>{removeEventListener("visibilitychange",se,!0),removeEventListener("prerenderingchange",se,!0)},tt=()=>{var t;if(U<0){let e=X();U=(document.prerendering||(t=globalThis.performance.getEntriesByType("visibility-state").filter(r=>r.name==="hidden"&&r.startTime>e)[0])==null?void 0:t.startTime)??qe(),He(),j(()=>{setTimeout(()=>{U=qe(),He()})})}return{get firstHiddenTime(){return U}}},le=t=>{document.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},je=[1800,3e3],nt=(t,e={})=>{le(()=>{let i=tt(),r,o=P("FCP"),a=q("paint",n=>{for(let s of n)s.name==="first-contentful-paint"&&(a.disconnect(),s.startTime<i.firstHiddenTime&&(o.value=Math.max(s.startTime-X(),0),o.entries.push(s),r(!0)))});a&&(r=C(t,o,je,e.reportAllChanges),j(n=>{o=P("FCP"),r=C(t,o,je,e.reportAllChanges),ke(()=>{o.value=performance.now()-n.timeStamp,r(!0)})}))})},Je=[.1,.25],Ve=t=>t.find(e=>{var i;return((i=e.node)==null?void 0:i.nodeType)===1})||t[0],rt=(t,e={})=>{let i=$(e=Object.assign({},e),oe),r=new WeakMap;i.t=o=>{if(o.sources.length){let a=Ve(o.sources);if(a){let n=(e.generateTarget??Le)(a.node);r.set(a,n)}}},((o,a={})=>{nt(Me(()=>{let n,s=P("CLS",0),l=$(a,oe),u=c=>{for(let p of c)l.u(p);l.o>s.value&&(s.value=l.o,s.entries=l.i,n())},f=q("layout-shift",u);f&&(n=C(o,s,Je,a.reportAllChanges),document.addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&(u(f.takeRecords()),n(!0))}),j(()=>{l.o=0,s=P("CLS",0),n=C(o,s,Je,a.reportAllChanges),ke(()=>n())}),setTimeout(n))}))})(o=>{let a=(n=>{let s={};if(n.entries.length){let l=n.entries.reduce((u,f)=>u.value>f.value?u:f);if(l!=null&&l.sources.length){let u=Ve(l.sources);u&&(s={largestShiftTarget:r.get(u),largestShiftTime:l.startTime,largestShiftValue:l.value,largestShiftSource:u,largestShiftEntry:l,loadState:ae(l.startTime)})}}return Object.assign(n,{attribution:s})})(o);t(a)},e)},it=(t,e={})=>{nt(i=>{let r=(o=>{let a={timeToFirstByte:0,firstByteToFCP:o.value,loadState:ae(et())};if(o.entries.length){let n=H(),s=o.entries.at(-1);if(n){let l=n.activationStart||0,u=Math.max(0,n.responseStart-l);a={timeToFirstByte:u,firstByteToFCP:o.value-u,loadState:ae(o.entries[0].startTime),navigationEntry:n,fcpEntry:s}}}return Object.assign(o,{attribution:a})})(i);t(r)},e)},ot=0,be=1/0,ie=0,Pt=t=>{for(let e of t)e.interactionId&&(be=Math.min(be,e.interactionId),ie=Math.max(ie,e.interactionId),ot=ie?(ie-be)/7+1:0)},Ce,Ge=()=>Ce?ot:performance.interactionCount??0,wt=()=>{"interactionCount"in performance||Ce||(Ce=q("event",Pt,{type:"event",buffered:!0,durationThreshold:0}))},Qe=0,ce=class{l=[];h=new Map;m;p;v(){Qe=Ge(),this.l.length=0,this.h.clear()}M(){let e=Math.min(this.l.length-1,Math.floor((Ge()-Qe)/50));return this.l[e]}u(e){var o,a;if((o=this.m)==null||o.call(this,e),!e.interactionId&&e.entryType!=="first-input")return;let i=this.l.at(-1),r=this.h.get(e.interactionId);if(r||this.l.length<10||e.duration>i.T){if(r?e.duration>r.T?(r.entries=[e],r.T=e.duration):e.duration===r.T&&e.startTime===r.entries[0].startTime&&r.entries.push(e):(r={id:e.interactionId,entries:[e],T:e.duration},this.h.set(r.id,r),this.l.push(r)),this.l.sort((n,s)=>s.T-n.T),this.l.length>10){let n=this.l.splice(10);for(let s of n)this.h.delete(s.id)}(a=this.p)==null||a.call(this,r)}}},Pe=t=>{let e=globalThis.requestIdleCallback||setTimeout;document.visibilityState==="hidden"?t():(e(t=Me(t)),document.addEventListener("visibilitychange",t,{once:!0}))},Ze=[200,500],at=(t,e={})=>{let i=$(e=Object.assign({},e),ce),r=[],o=[],a=0,n=new WeakMap,s=new WeakMap,l=!1,u=()=>{l||(Pe(f),l=!0)},f=()=>{let d=i.l.map(T=>n.get(T.entries[0])),m=o.length-50;o=o.filter((T,y)=>y>=m||d.includes(T));let g=new Set;for(let T of o){let y=c(T.startTime,T.processingEnd);for(let S of y)g.add(S)}let h=r.length-1-50;r=r.filter((T,y)=>T.startTime>a&&y>h||g.has(T)),l=!1};i.m=d=>{let m=d.startTime+d.duration,g;a=Math.max(a,d.processingEnd);for(let h=o.length-1;h>=0;h--){let T=o[h];if(Math.abs(m-T.renderTime)<=8){g=T,g.startTime=Math.min(d.startTime,g.startTime),g.processingStart=Math.min(d.processingStart,g.processingStart),g.processingEnd=Math.max(d.processingEnd,g.processingEnd),g.entries.push(d);break}}g||(g={startTime:d.startTime,processingStart:d.processingStart,processingEnd:d.processingEnd,renderTime:m,entries:[d]},o.push(g)),(d.interactionId||d.entryType==="first-input")&&n.set(d,g),u()},i.p=d=>{if(!s.get(d)){let m=(e.generateTarget??Le)(d.entries[0].target);s.set(d,m)}};let c=(d,m)=>{let g=[];for(let h of r)if(!(h.startTime+h.duration<d)){if(h.startTime>m)break;g.push(h)}return g},p=d=>{let m=d.entries[0],g=n.get(m),h=m.processingStart,T=Math.max(m.startTime+m.duration,h),y=Math.min(g.processingEnd,T),S=g.entries.sort((v,b)=>v.processingStart-b.processingStart),F=c(m.startTime,y),M=i.h.get(m.interactionId),A={interactionTarget:s.get(M),interactionType:m.name.startsWith("key")?"keyboard":"pointer",interactionTime:m.startTime,nextPaintTime:T,processedEventEntries:S,longAnimationFrameEntries:F,inputDelay:h-m.startTime,processingDuration:y-h,presentationDelay:T-y,loadState:ae(m.startTime),longestScript:void 0,totalScriptDuration:void 0,totalStyleAndLayoutDuration:void 0,totalPaintDuration:void 0,totalUnattributedDuration:void 0};return(v=>{var xe;if(!((xe=v.longAnimationFrameEntries)!=null&&xe.length))return;let b=v.interactionTime,z=v.inputDelay,Y=v.processingDuration,D,_,ue=0,J=0,me=0,ge=0;for(let K of v.longAnimationFrameEntries){J=J+K.startTime+K.duration-K.styleAndLayoutStart;for(let w of K.scripts){let Fe=w.startTime+w.duration;if(Fe<b)continue;let ee=Fe-Math.max(b,w.startTime),Ae=w.duration?ee/w.duration*w.forcedStyleAndLayoutDuration:0;ue+=ee-Ae,J+=Ae,ee>ge&&(_=w.startTime<b+z?"input-delay":w.startTime>=b+z+Y?"presentation-delay":"processing-duration",D=w,ge=ee)}}let fe=v.longAnimationFrameEntries.at(-1),De=fe?fe.startTime+fe.duration:0;De>=b+z+Y&&(me=v.nextPaintTime-De),D&&_&&(v.longestScript={entry:D,subpart:_,intersectingDuration:ge}),v.totalScriptDuration=ue,v.totalStyleAndLayoutDuration=J,v.totalPaintDuration=me,v.totalUnattributedDuration=v.nextPaintTime-b-ue-J-me})(A),Object.assign(d,{attribution:A})};q("long-animation-frame",d=>{r=r.concat(d),u()}),((d,m={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&le(()=>{wt();let g,h=P("INP"),T=$(m,ce),y=F=>{Pe(()=>{for(let A of F)T.u(A);let M=T.M();M&&M.T!==h.value&&(h.value=M.T,h.entries=M.entries,g())})},S=q("event",y,{durationThreshold:m.durationThreshold??40});g=C(d,h,Ze,m.reportAllChanges),S&&(S.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&(y(S.takeRecords()),g(!0))}),j(()=>{T.v(),h=P("INP"),g=C(d,h,Ze,m.reportAllChanges)}))})})(d=>{let m=p(d);t(m)},e)},de=class{m;u(e){var i;(i=this.m)==null||i.call(this,e)}},Xe=[2500,4e3],st=(t,e={})=>{let i=$(e=Object.assign({},e),de),r=new WeakMap;i.m=o=>{if(o.element){let a=(e.generateTarget??Le)(o.element);r.set(o,a)}},((o,a={})=>{le(()=>{let n=tt(),s,l=P("LCP"),u=$(a,de),f=p=>{a.reportAllChanges||(p=p.slice(-1));for(let d of p)u.u(d),d.startTime<n.firstHiddenTime&&(l.value=Math.max(d.startTime-X(),0),l.entries=[d],s())},c=q("largest-contentful-paint",f);if(c){s=C(o,l,Xe,a.reportAllChanges);let p=Me(()=>{f(c.takeRecords()),c.disconnect(),s(!0)});for(let d of["keydown","click","visibilitychange"])addEventListener(d,()=>Pe(p),{capture:!0,once:!0});j(d=>{l=P("LCP"),s=C(o,l,Xe,a.reportAllChanges),ke(()=>{l.value=performance.now()-d.timeStamp,s(!0)})})}})})(o=>{let a=(n=>{let s={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:n.value};if(n.entries.length){let l=H();if(l){let u=l.activationStart||0,f=n.entries.at(-1),c=f.url&&performance.getEntriesByType("resource").filter(g=>g.name===f.url)[0],p=Math.max(0,l.responseStart-u),d=Math.max(p,c?(c.requestStart||c.startTime)-u:0),m=Math.min(n.value,Math.max(d,c?c.responseEnd-u:0));s={target:r.get(f),timeToFirstByte:p,resourceLoadDelay:d-p,resourceLoadDuration:m-d,elementRenderDelay:n.value-m,navigationEntry:l,lcpEntry:f},f.url&&(s.url=f.url),c&&(s.lcpResourceEntry=c)}}return Object.assign(n,{attribution:s})})(o);t(a)},e)},Ye=[800,1800],we=t=>{document.prerendering?le(()=>we(t)):document.readyState!=="complete"?addEventListener("load",()=>we(t),!0):setTimeout(t)},ct=(t,e={})=>{((i,r={})=>{let o=P("TTFB"),a=C(i,o,Ye,r.reportAllChanges);we(()=>{let n=H();n&&(o.value=Math.max(n.responseStart-X(),0),o.entries=[n],a(!0),j(()=>{o=P("TTFB",0),a=C(i,o,Ye,r.reportAllChanges),a(!0)}))})})(i=>{let r=(o=>{let a={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(o.entries.length){let n=o.entries[0],s=n.activationStart||0,l=Math.max((n.workerStart||n.fetchStart)-s,0),u=Math.max(n.domainLookupStart-s,0),f=Math.max(n.connectStart-s,0),c=Math.max(n.connectEnd-s,0);a={waitingDuration:l,cacheDuration:u-l,dnsDuration:f-u,connectionDuration:c-f,requestDuration:o.value-c,navigationEntry:n}}return Object.assign(o,{attribution:a})})(i);t(r)},e)};function Lt(t){for(let e in t)if(t[e]!==void 0)return!0;return!1}function B(t){return Lt(t)?t:void 0}function lt(){let t=document.getElementById("main");if(!t)return;let e=new Set;try{let a=n=>e.add(n);st(a),it(a),rt(({value:n,...s})=>{e.add({...s,value:n*1e3})}),at(a),ct(a)}catch{}let i=new Set([...performance.getEntriesByType("mark"),...performance.getEntriesByType("measure")].filter(a=>a.name.startsWith("framer-")));new PerformanceObserver(a=>{a.getEntries().forEach(n=>{n.name.startsWith("framer-")&&i.add(n)})}).observe({entryTypes:["measure","mark"]});let r=t.dataset,o={pageOptimizedAt:r.framerPageOptimizedAt?new Date(r.framerPageOptimizedAt).getTime():null,ssrReleasedAt:r.framerSsrReleasedAt?new Date(r.framerSsrReleasedAt).getTime():null,origin:document.location.origin,pathname:document.location.pathname,search:document.location.search};R(()=>At(o),"load"),R(()=>kt(e,i,o),"lazy")}var dt=!1;function kt(t,e,i){let r=document.getElementById("main");if(!r)return;let o=[];if(dt||(o.push(Mt(i,r)),dt=!0),t.size>0&&(o.push(...xt(t,i)),t.clear()),e.size>0){let a=Ft(e);a&&o.push(a),e.clear()}k(o)}function Mt({pageOptimizedAt:t,ssrReleasedAt:e,origin:i,pathname:r,search:o},a){var l,u,f,c,p,d;let n=performance.getEntriesByType("navigation")[0],s=document.querySelector("[data-framer-css-ssr-minified]");return new E("published_site_performance",{hydrationDurationMs:null,pageLoadDurationMs:null,domNodes:document.getElementsByTagName("*").length,resourcesCount:performance.getEntriesByType("resource").length,headSize:document.head.innerHTML.length,framerCSSSize:(l=s==null?void 0:s.textContent)==null?void 0:l.length,modulePreloads:document.querySelectorAll(`link[rel="modulepreload"][href^="${L().cdn}"]`).length,hasPageContent:a.dataset["framer-no-content"]===void 0,timeZone:V,pageOptimizedAt:t,ssrReleasedAt:e,devicePixelRatio:window.devicePixelRatio,timeToFirstByteMs:null,navigationTiming:n?{activationStart:n.activationStart,connectEnd:n.connectEnd,connectStart:n.connectStart,criticalCHRestart:n.criticalCHRestart,decodedBodySize:n.decodedBodySize,deliveryType:n.deliveryType,domComplete:n.domComplete,domContentLoadedEventEnd:n.domContentLoadedEventEnd,domContentLoadedEventStart:n.domContentLoadedEventStart,domInteractive:n.domInteractive,domainLookupEnd:n.domainLookupEnd,domainLookupStart:n.domainLookupStart,duration:n.duration,encodedBodySize:n.encodedBodySize,fetchStart:n.fetchStart,firstInterimResponseStart:n.firstInterimResponseStart,loadEventEnd:n.loadEventEnd,loadEventStart:n.loadEventStart,nextHopProtocol:n.nextHopProtocol,redirectCount:n.redirectCount,redirectEnd:n.redirectEnd,redirectStart:n.redirectStart,requestStart:n.requestStart,responseEnd:n.responseEnd,responseStart:n.responseStart,responseStatus:n.responseStatus,secureConnectionStart:n.secureConnectionStart,serverTiming:n.serverTiming?JSON.stringify(n.serverTiming):null,startTime:n.startTime,transferSize:n.transferSize,type:n.type,unloadEventEnd:n.unloadEventEnd,unloadEventStart:n.unloadEventStart,workerStart:n.workerStart}:void 0,connection:B({downlink:(u=navigator.connection)==null?void 0:u.downlink,downlinkMax:(f=navigator.connection)==null?void 0:f.downlinkMax,rtt:(c=navigator.connection)==null?void 0:c.rtt,saveData:(p=navigator.connection)==null?void 0:p.saveData,type:(d=navigator.connection)==null?void 0:d.type}),context:{origin:i,pathname:r,search:o}})}var Dt=0;function xt(t,{pageOptimizedAt:e,ssrReleasedAt:i,origin:r,pathname:o,search:a}){let n=[];return t.forEach(s=>{t.delete(s);let{name:l,value:u,id:f,attribution:c}=s,p={metric:l,label:f,value:Math.round(u),counter:Dt++,pageOptimizedAt:e,ssrReleasedAt:i,context:{origin:r,pathname:o,search:a},attributionLcp:void 0,attributionCls:void 0,attributionInp:void 0,attributionFcp:void 0,attributionTtfb:void 0};l==="LCP"?p.attributionLcp=B({element:c.target,timeToFirstByte:c.timeToFirstByte,resourceLoadDelay:c.resourceLoadDelay,resourceLoadTime:c.resourceLoadDuration,elementRenderDelay:c.elementRenderDelay,url:c.url}):l==="CLS"?p.attributionCls=B({largestShiftTarget:c.largestShiftTarget,largestShiftTime:c.largestShiftTime,largestShiftValue:c.largestShiftValue,loadState:c.loadState}):l==="INP"?p.attributionInp=B({eventTarget:c.interactionTarget,eventType:c.interactionType,eventTime:c.interactionTime?Math.round(c.interactionTime):void 0,loadState:c.loadState,inputDelay:c.inputDelay,processingDuration:c.processingDuration,presentationDelay:c.presentationDelay,nextPaintTime:c.nextPaintTime,totalScriptDuration:c.totalScriptDuration,totalStyleAndLayoutDuration:c.totalStyleAndLayoutDuration,totalPaintDuration:c.totalPaintDuration,totalUnattributedDuration:c.totalUnattributedDuration,longestScript:JSON.stringify(c.longestScript)}):l==="FCP"?p.attributionFcp=B({timeToFirstByte:c.timeToFirstByte,firstByteToFCP:c.firstByteToFCP,loadState:c.loadState}):l==="TTFB"&&(p.attributionTtfb=B({waitingTime:c.waitingDuration,dnsTime:c.dnsDuration,connectionTime:c.connectionDuration,requestTime:c.requestDuration,cacheDuration:c.cacheDuration})),n.push(new E("published_site_performance_web_vitals",p))}),n}function Ft(t){let e=[];if(t.forEach(i=>{t.delete(i);let{name:r,startTime:o,duration:a,detail:n}=i,s={name:r,startTime:o,duration:a,detail:n};e.push(s)}),e.length!==0)return new E("published_site_performance_user_timings",{timings:JSON.stringify(e)})}async function At({origin:t,pathname:e,search:i}){let r=document.getElementById("main");if(!r)return;await I();let o=1/0,a=null,n=null,s=0,l=0,u=0,f=0,c=L().cdn,p=`^${c}[^/]+/`,d=".[^.]+.mjs$",m=new RegExp(`${p}script_main${d}`),g=new RegExp(`${p}framer${d}`),h=new RegExp(`${p}motion${d}`),T=performance.getEntriesByType("resource"),y=T.length;for(let F=0;F<y;F++){let M=T[F],{deliveryType:A,initiatorType:v,transferSize:b,decodedBodySize:z,encodedBodySize:Y,name:D,startTime:_}=M;_>o||!(v==="script"&&D.startsWith(c))||(++u,(A==="cache"||A!==void 0&&b===0)&&++f,s+=Y,l+=z,m.test(D)?o=_:a===null&&g.test(D)?a=z:n===null&&h.test(D)&&(n=z))}await I();let S=performance.getEntriesByType("navigation")[0];k([new E("published_site_performance_load",{pageLoadDurationMs:(S==null?void 0:S.domContentLoadedEventEnd)!==void 0&&S.domContentLoadedEventStart!==void 0?Math.round(S.domContentLoadedEventEnd-S.domContentLoadedEventStart):null,resourcesCount:y,domNodes:document.getElementsByTagName("*").length,headSize:document.head.innerHTML.length,headDomNodes:document.head.getElementsByTagName("*").length,bodySize:document.body.innerHTML.length,bodyDomNodes:document.body.getElementsByTagName("*").length,reactRootSize:r.innerHTML.length,reactRootDomNodes:r.getElementsByTagName("*").length,jsSizeDecoded:l,jsSizeEncoded:s,jsCountCached:f,jsCountTotal:u,mainScriptStartTime:Number.isFinite(o)?o:null,libraryJSSizeDecoded:a,motionJSSizeDecoded:n,context:{origin:t,pathname:e,search:i}})])}function ut(){window.__send_framer_event=(t,e)=>{let i=new E(t,e);k([i],"eager")}}var zt=L(),Rt=async()=>{if(await I(!0),ze(),zt.trackNavigation){$e();let t=typeof document.referrer=="string";Z({initialReferrer:t&&document.referrer||null})}lt(),ut(),Ue()};Rt();})();
   Risk: MEDIUM - Comments may contain sensitive information

