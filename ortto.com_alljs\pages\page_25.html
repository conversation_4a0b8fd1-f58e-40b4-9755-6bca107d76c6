<!DOCTYPE html>
<!-- â¨ Built with Framer â¢ https://www.framer.com/ -->
<html lang="en-US">
 <head>
  <meta charset="utf-8"/>
  <!-- Start of headStart -->
  <!-- Prevent pixel tracking from messing up footer -->
  <!-- End of headStart -->
  <meta content="width=device-width" name="viewport"/>
  <meta content="Framer 6d96db3" name="generator"/>
  <title>
   Product Updates | Ortto
  </title>
  <meta content="We're constantly innovating. Find a roundup of the latest releases, improvements and fixes in our product updates hub. Learn more!" name="description"/>
  <meta content="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/searchIndex-PbKrUcIf7GOH.json" name="framer-search-index"/>
  <link href="https://framerusercontent.com/images/GkX2JJPejNVZXg3YnqjD7pORA.png" media="(prefers-color-scheme: light)" rel="icon"/>
  <link href="https://framerusercontent.com/images/GkX2JJPejNVZXg3YnqjD7pORA.png" media="(prefers-color-scheme: dark)" rel="icon"/>
  <!-- Open Graph / Facebook -->
  <meta content="website" property="og:type"/>
  <meta content="Product Updates | Ortto" property="og:title"/>
  <meta content="We're constantly innovating. Find a roundup of the latest releases, improvements and fixes in our product updates hub. Learn more!" property="og:description"/>
  <meta content="https://framerusercontent.com/assets/apqyC5hVg0GWDr28BAdpC9jsmA.png" property="og:image"/>
  <!-- Twitter -->
  <meta content="summary_large_image" name="twitter:card"/>
  <meta content="Product Updates | Ortto" name="twitter:title"/>
  <meta content="We're constantly innovating. Find a roundup of the latest releases, improvements and fixes in our product updates hub. Learn more!" name="twitter:description"/>
  <meta content="https://framerusercontent.com/assets/apqyC5hVg0GWDr28BAdpC9jsmA.png" name="twitter:image"/>
  <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
  <meta content="max-image-preview:large" name="robots"/>
  <link href="https://ortto.com/product-updates/" rel="canonical"/>
  <meta content="https://ortto.com/product-updates/" property="og:url"/>
  <!-- Start of headEnd -->
  <!-- This contains Ortto tracking code, Google Site verification, Google tag manager code, and Capture form styling overrides. -->
  <!-- Ortto manhattan capture code -->
  <script>
   function handleSubmitCallback(data) {

        // Check if current page contains 'contact/sales/'

        if (window.location.pathname.includes('contact/sales/')) {

            console.log('Sales contact form submitted', JSON.stringify(data));

            window.dataLayer = window.dataLayer || [];

            window.dataLayer.push({

            'event': 'contact_sales_form_submission',

            'formId': 'your-form-id',

            'formName': 'Contact Form'

            });

        }

    }



    window.ap3c = window.ap3c || {};

    var ap3c = window.ap3c;

    ap3c.cmd = ap3c.cmd || [];

    ap3c.cmd.push(function() {

        ap3c.init('YBIzPQ0ICL0fJP-ubWFuaGF0dGFu', 'https://t.ortto.com/', {submitCallback: handleSubmitCallback});

        ap3c.track({v: 0});

    });

    ap3c.activity = function(act) { ap3c.act = (ap3c.act || []); ap3c.act.push(act); };

    var s, t; s = document.createElement('script'); s.type = 'text/javascript'; s.src = "https://t.ortto.com/app.js";

    t = document.getElementsByTagName('script')[0]; t.parentNode.insertBefore(s, t);
  </script>
  <meta content="SSKvaJmkcJQwC1gE1Lbc_OeLpsJuRMLgVEclp6LdG_Y" name="google-site-verification"/>
  <script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-KQWCZZD">
  </script>
  <script data-nscript="afterInteractive">
   (function(w, d, s, l, i) {

      w[l] = w[l] || [];

      w[l].push({

          'gtm.start': new Date().getTime(),

          event: 'gtm.js'

      });

      var f = d.getElementsByTagName(s)[0],

          j = d.createElement(s),

          dl = l != 'dataLayer' ? '&l=' + l : '';

      j.async = true;

      j.src =

          'https://www.googletagmanager.com/gtm.js?id=' + i + dl;

      f.parentNode.insertBefore(j, f);

  })(window, document, 'script', 'dataLayer', 'GTM-KQWCZZD');
  </script>
  <!-- End of headEnd -->
 </head>
 <body class="framer-body-MA0lYU1TT-framer-PUeXB">
  <script async="" data-fid="35706bdfafeb53aac45145f1fc2a0eac5f7e285db605f7c0e55bde5643c190f3" data-no-nt="" src="https://events.framer.com/script?v=2">
  </script>
  <!-- Start of bodyStart -->
  <!-- End of bodyStart -->
  <div data-framer-generated-page="" data-framer-hydrate-v2='{"routeId":"MA0lYU1TT","localeId":"default","breakpoints":[{"hash":"q4xlbk","mediaQuery":"(min-width: 1400px)"},{"hash":"11j12rb","mediaQuery":"(min-width: 1024px) and (max-width: 1399px)"},{"hash":"29ubib","mediaQuery":"(min-width: 810px) and (max-width: 1023px)"},{"hash":"13ggqhr","mediaQuery":"(max-width: 809px)"}]}' data-framer-page-optimized-at="2025-07-10T10:06:16.761Z" data-framer-ssr-released-at="2025-07-02T08:08:42.657Z" id="main">
   <!--$-->
   <div class="framer-PUeXB framer-EvwNn framer-Q7j2r framer-nxPt4 framer-rTzsG framer-4GIj9 framer-d9Mk0 framer-phynS framer-vlcKs framer-j0Dxz framer-x99CK framer-n93qy framer-uunPM framer-2KVFG framer-YQsNO framer-q4xlbk" data-framer-root="" style="min-height:100vh;width:auto">
    <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
     <div class="framer-1el9ij4-container hidden-q4xlbk hidden-11j12rb" data-framer-name="Navbar 2024 Mobile" id="1el9ij4" name="Navbar 2024 Mobile">
      <div class="framer-vwp8l framer-irM8r framer-145jfro framer-v-145jfro" data-framer-name="Mobile" name="Navbar 2024 Mobile" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31));height:100%;width:100%">
       <div class="framer-1ojkeju" data-framer-name="Navbar" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31))">
        <div class="framer-18suagi" data-framer-name="Container">
         <!--$-->
         <a aria-label="Logo" class="framer-oeb4r3 framer-1ocx6j3" data-framer-name="Logo" href="../" style="transform:translateY(-50%)">
          <div aria-hidden="true" class="framer-1ynz9u6" data-framer-component-type="SVG" data-framer-name="SVG" style="image-rendering:pixelated;flex-shrink:0;fill:black;color:black">
           <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit">
            <svg style="width:100%;height:100%;" viewbox="0 0 57 36">
             <use href="#svg-107607693_742">
             </use>
            </svg>
           </div>
          </div>
         </a>
         <!--/$-->
         <div class="framer-1iv1r7c" data-framer-name="Flex">
          <div class="framer-1lb31o6">
           <div class="framer-8sfqjg-container" data-framer-name="Pricing" name="Pricing">
            <!--$-->
            <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="https://ortto.com/login" name="Pricing" rel="noopener" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
             <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
              <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
               <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
                Sign in
               </p>
              </div>
             </div>
            </a>
            <!--/$-->
           </div>
          </div>
          <div class="framer-ugqzu0" data-framer-name="Mobile Menu Button" data-highlight="true" tabindex="0">
           <div class="framer-7cy8sx" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
           <div class="framer-dtu78n" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;opacity:1">
           </div>
           <div class="framer-lt8czn" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
     <div class="framer-1el9ij4-container hidden-q4xlbk hidden-11j12rb" data-framer-name="Navbar 2024 Mobile" id="1el9ij4" name="Navbar 2024 Mobile">
      <div class="framer-vwp8l framer-irM8r framer-145jfro framer-v-145jfro" data-framer-name="Mobile" name="Navbar 2024 Mobile" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31));height:100%;width:100%">
       <div class="framer-1ojkeju" data-framer-name="Navbar" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31))">
        <div class="framer-18suagi" data-framer-name="Container">
         <!--$-->
         <a aria-label="Logo" class="framer-oeb4r3 framer-1ocx6j3" data-framer-name="Logo" href="../" style="transform:translateY(-50%)">
          <div aria-hidden="true" class="framer-1ynz9u6" data-framer-component-type="SVG" data-framer-name="SVG" style="image-rendering:pixelated;flex-shrink:0;fill:black;color:black">
           <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit">
            <svg style="width:100%;height:100%;" viewbox="0 0 57 36">
             <use href="#svg-107607693_742">
             </use>
            </svg>
           </div>
          </div>
         </a>
         <!--/$-->
         <div class="framer-1iv1r7c" data-framer-name="Flex">
          <div class="framer-1lb31o6">
           <div class="framer-8sfqjg-container" data-framer-name="Pricing" name="Pricing">
            <!--$-->
            <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="https://ortto.com/login" name="Pricing" rel="noopener" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
             <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
              <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
               <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
                Sign in
               </p>
              </div>
             </div>
            </a>
            <!--/$-->
           </div>
          </div>
          <div class="framer-ugqzu0" data-framer-name="Mobile Menu Button" data-highlight="true" tabindex="0">
           <div class="framer-7cy8sx" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
           <div class="framer-dtu78n" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;opacity:1">
           </div>
           <div class="framer-lt8czn" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
     <div class="framer-1el9ij4-container hidden-q4xlbk hidden-11j12rb" data-framer-name="Navbar 2024 Mobile" id="1el9ij4" name="Navbar 2024 Mobile">
      <div class="framer-vwp8l framer-irM8r framer-145jfro framer-v-145jfro" data-framer-name="Mobile" name="Navbar 2024 Mobile" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31));height:100%;width:100%">
       <div class="framer-1ojkeju" data-framer-name="Navbar" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31))">
        <div class="framer-18suagi" data-framer-name="Container">
         <!--$-->
         <a aria-label="Logo" class="framer-oeb4r3 framer-1ocx6j3" data-framer-name="Logo" href="../" style="transform:translateY(-50%)">
          <div aria-hidden="true" class="framer-1ynz9u6" data-framer-component-type="SVG" data-framer-name="SVG" style="image-rendering:pixelated;flex-shrink:0;fill:black;color:black">
           <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit">
            <svg style="width:100%;height:100%;" viewbox="0 0 57 36">
             <use href="#svg-107607693_742">
             </use>
            </svg>
           </div>
          </div>
         </a>
         <!--/$-->
         <div class="framer-1iv1r7c" data-framer-name="Flex">
          <div class="framer-1lb31o6">
           <div class="framer-8sfqjg-container" data-framer-name="Pricing" name="Pricing">
            <!--$-->
            <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="https://ortto.com/login" name="Pricing" rel="noopener" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
             <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
              <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
               <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
                Sign in
               </p>
              </div>
             </div>
            </a>
            <!--/$-->
           </div>
          </div>
          <div class="framer-ugqzu0" data-framer-name="Mobile Menu Button" data-highlight="true" tabindex="0">
           <div class="framer-7cy8sx" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
           <div class="framer-dtu78n" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;opacity:1">
           </div>
           <div class="framer-lt8czn" style="background-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px;transform:none">
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="framer-1vbuain-container hidden-29ubib hidden-13ggqhr" data-framer-name="Navbar 2024 Desktop" id="1vbuain" name="Navbar 2024 Desktop">
     <div class="framer-kZZxz framer-19zk5tq framer-v-19zk5tq" data-framer-name="Desktop" name="Navbar 2024 Desktop" style="width:100%">
      <div class="framer-aa5gvh" data-framer-name="Navbar" style="background-color:var(--token-942b26be-0f2b-4d89-a85e-6c784ba31d9a, rgb(20, 23, 31))">
       <div class="framer-a3jhhm" data-framer-name="Container">
        <!--$-->
        <a aria-label="Logo" class="framer-1cx8wed framer-1i018e8" data-framer-name="Logo" href="../">
         <div aria-hidden="true" class="framer-vyakth" data-framer-component-type="SVG" data-framer-name="White" style="image-rendering:pixelated;flex-shrink:0;fill:black;color:black">
          <div class="svgContainer" style="width:100%;height:100%;aspect-ratio:inherit">
           <svg style="width:100%;height:100%;" viewbox="0 0 57 36">
            <use href="#svg-107607693_742">
            </use>
           </svg>
          </div>
         </div>
        </a>
        <!--/$-->
        <div class="framer-mo1syt" data-framer-name="Flex">
         <div class="framer-idpjj9-container" data-framer-name="Product" name="Product">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-o6lnvz framer-1ywttu1" data-framer-name="Dropdown" data-highlight="true" name="Product" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
              Product
             </p>
            </div>
            <div class="framer-1s9liqu-container" data-framer-name="Icon" name="Icon" style="opacity:0.65">
             <div style="display:contents">
             </div>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <div class="framer-1bybivp-container" data-framer-name="Solutions" name="Solutions">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-o6lnvz framer-1ywttu1" data-framer-name="Dropdown" data-highlight="true" name="Solutions" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
              Solutions
             </p>
            </div>
            <div class="framer-1s9liqu-container" data-framer-name="Icon" name="Icon" style="opacity:0.65">
             <div style="display:contents">
             </div>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <div class="framer-8vvvro-container" data-framer-name="Resources" name="Resources">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-o6lnvz framer-1ywttu1" data-framer-name="Dropdown" data-highlight="true" name="Resources" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
              Resources
             </p>
            </div>
            <div class="framer-1s9liqu-container" data-framer-name="Icon" name="Icon" style="opacity:0.65">
             <div style="display:contents">
             </div>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <div class="framer-12qbij8-container" data-framer-name="Templates" name="Templates">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="https://ortto.com/templates/" name="Templates" rel="noopener" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
              Templates
             </p>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <!--$-->
         <div class="framer-gkip3d-container" data-framer-name="Pricing" name="Pricing">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="../pricing/" name="Pricing" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
              Pricing
             </p>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <!--/$-->
        </div>
        <div class="framer-1yannrs" data-framer-name="Flex">
         <div class="framer-1xmzioj">
          <!--$-->
          <div class="framer-l3764l-container" data-framer-name="Pricing" name="Pricing">
           <!--$-->
           <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="../contact/sales/" name="Pricing" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
             <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
              <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
               Book a demo
              </p>
             </div>
            </div>
           </a>
           <!--/$-->
          </div>
          <!--/$-->
          <div class="framer-13ejaeq-container" data-framer-name="Pricing" name="Pricing">
           <!--$-->
           <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-1kh1i30 framer-1ywttu1" data-framer-name="Link" href="https://ortto.com/login" name="Pricing" rel="noopener" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
            <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:rgba(0, 0, 0, 0);border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
             <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--variable-reference-Gt7AbCozJ-HuQPwp750);--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
              <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--variable-reference-Gt7AbCozJ-HuQPwp750))">
               Sign in
              </p>
             </div>
            </div>
           </a>
           <!--/$-->
          </div>
         </div>
         <!--$-->
         <div class="framer-8dyxci-container" data-framer-name="Pricing" name="Pricing">
          <!--$-->
          <a class="framer-RBGBV framer-u3cww framer-1kh1i30 framer-v-814txg framer-1ywttu1" data-framer-name="Button" href="../trial/" name="Pricing" style="border-bottom-left-radius:4px;border-bottom-right-radius:4px;border-top-left-radius:4px;border-top-right-radius:4px">
           <div class="framer-1p36w59" data-framer-name="Button" style="--border-bottom-width:0px;--border-color:rgba(0, 0, 0, 0);--border-left-width:0px;--border-right-width:0px;--border-style:solid;--border-top-width:0px;background-color:var(--token-d0f1cb73-9a81-45b9-98eb-6327ca9ce4ae, rgb(16, 112, 255));border-bottom-left-radius:999px;border-bottom-right-radius:999px;border-top-left-radius:999px;border-top-right-radius:999px">
            <div class="framer-1wqgeis" data-framer-component-type="RichTextContainer" data-framer-name="Try Ortto free" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;--extracted-r6o4lv:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));--variable-reference-Gt7AbCozJ-HuQPwp750:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255));transform:none">
             <p class="framer-text framer-styles-preset-daymwu" data-styles-preset="HS7etqF7N" style="--framer-text-color:var(--extracted-r6o4lv, var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255)))">
              Start trial
             </p>
            </div>
           </div>
          </a>
          <!--/$-->
         </div>
         <!--/$-->
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="framer-4yqbq8" data-framer-name="Hero" name="Hero">
     <div class="framer-1dr2gdr" data-framer-name="Container" name="Container">
      <div class="framer-orwtm">
       <div class="framer-1qoe702">
        <div class="framer-13hlfdo" data-framer-component-type="RichTextContainer" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
         <h1 class="framer-text framer-styles-preset-1qq8e0f" data-styles-preset="MdKsfxCZ2" style="--framer-text-alignment:center;--framer-text-color:var(--token-7edd297b-3eb0-482e-b3f2-f1ad3ed5e2ad, rgb(76, 128, 255))">
          product Updates
         </h1>
        </div>
        <div class="framer-1noqwsx" data-framer-component-type="RichTextContainer" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
         <p class="framer-text framer-styles-preset-1350cec" data-styles-preset="YcFOfAQEt" style="--framer-text-alignment:center;--framer-text-color:var(--token-4d4263d3-a7e3-4398-8db5-66f59f6896db, rgb(255, 255, 255))">
          New releases, improvements, &amp; fixes
         </p>
        </div>
       </div>
       <div class="framer-vw3sex">
        <div class="framer-12vrubd" data-framer-component-type="RichTextContainer" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
         <p class="framer-text framer-styles-preset-1io8p8s" data-styles-preset="OTC_WhD6Z">
          Subscribe to product updates or
          <!--$-->
          <a class="framer-text framer-styles-preset-1wicq5s" data-styles-preset="ro7OPezbn" href="https://www.linkedin.com/company/ortto/" rel="noopener" target="_blank">
           follow us on LinkedIn
          </a>
          <!--/$-->
          .
         </p>
        </div>
        <div class="framer-1enuhhi-container">
         <div class="form-style-overrides" id="product-updates">
          <div>
           <div id="649a4f772cb13f69c0437c84" style="width: 100%; height: 100%;">
            <div class="649a4f772cb13f69c0437c84-template" id="649a4f772cb13f69c0437c84-form" style="position: relative; display: flex; height: 100%; align-items: center; justify-content: center;">
             <div class="ap3w-embeddable-form-649a4f772cb13f69c0437c84 ap3w-embeddable-form-649a4f772cb13f69c0437c84-full ap3w-embeddable-form-649a4f772cb13f69c0437c84-solid" data-select="true" id="selected-_p7rdq6usc">
              <form class="ap3w-embeddable-form-content" id="ap3w-embeddable-form-649a4f772cb13f69c0437c84">
               <div class="ap3w-form-input ap3w-form-input-649a4f772cb13f69c0437c84" data-field-id="str::email" data-merge-strategy="override" data-select="true" id="selected-_k3to7t18g">
                <label class="ap3w-form-input-label" for="ap3w-form-input-email-649a4f772cb13f69c0437c84">
                 Email address*
                </label>
                <input id="ap3w-form-input-email-649a4f772cb13f69c0437c84" name="email" placeholder="<EMAIL>" required="" step="1" type="email"/>
               </div>
               <div class="ap3w-form-button ap3w-form-button-649a4f772cb13f69c0437c84" id="selected-_n2f1p4oxu">
                <button data-button-on-click="thank-you" data-select="true" id="ap3w-form-button-649a4f772cb13f69c0437c84" type="submit">
                 Subscribe
                </button>
               </div>
              </form>
             </div>
            </div>
            <div class="649a4f772cb13f69c0437c84-template" id="649a4f772cb13f69c0437c84-thank-you" style="position: relative; display: none; height: 100%; align-items: center; justify-content: center;">
             <div class="ap3w-embeddable-form-649a4f772cb13f69c0437c84 ap3w-embeddable-form-649a4f772cb13f69c0437c84-full ap3w-embeddable-form-649a4f772cb13f69c0437c84-solid" data-select="true" id="selected-_uvnpkodwx">
              <form class="ap3w-embeddable-form-content" id="ap3w-embeddable-form-649a4f772cb13f69c0437c84">
               <div class="ap3w-text ap3w-text-649a4f772cb13f69c0437c84 ap3w-text--first ap3w-text--last" id="selected-_1v4p0kycp">
                <div data-select="true">
                 <p data-size="h2">
                  Youâre subscribed.
                 </p>
                 <p>
                  Youâll receive product updates to the email provided. You can unsubscribe at any time.
                 </p>
                </div>
               </div>
              </form>
             </div>
            </div>
           </div>
          </div>
         </div>
        </div>
        <div class="framer-xh4ock-container" data-framer-name="Form CSS" name="Form CSS">
         <div style="width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center">
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
    <div class="framer-10pwmaq" data-framer-name="Content" name="Content">
     <div class="framer-1d6bw44-container">
      <div style="width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center">
      </div>
     </div>
     <div class="framer-bru1dt" data-framer-name="Container" name="Container">
      <div class="framer-efb4i2">
       <!--$-->
       <div class="framer-1q7sc0w">
        <div class="framer-1gomkm6" data-framer-name="Content" name="Content">
         <div class="framer-su41v0">
          <div class="framer-hvot7y" data-framer-component-type="RichTextContainer" data-framer-name="Date" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1rzok9v" data-styles-preset="Ydq3RYFlI" style="--framer-text-color:var(--token-e119c063-d39f-482e-9d29-7989ac59aa0e, rgb(109, 107, 112))">
            June 26, 2025
           </p>
          </div>
          <div class="framer-10qvk08" data-framer-component-type="RichTextContainer" data-framer-name="Title" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1y7h2ud" data-styles-preset="OFIy16ZPO">
            June Update
           </p>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 120px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png" srcset="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=512 512w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 48px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png" srcset="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=512 512w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 80px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png" srcset="https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=512 512w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/Kdof9RANK4fEuAHiQJH7vdJrco.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="framer-11f1b56" data-framer-component-type="RichTextContainer" data-framer-name="Content" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            Take control of your data with API updates, Salesforce connectivity improvements and more.
           </strong>
          </h3>
          <h5 class="framer-text framer-styles-preset-t34kef">
           Do you have feedback or suggestions? You can join the discussion
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/" rel="noopener" target="_blank">
            here
           </a>
           <!--/$-->
           (login with your Ortto account credentials).
          </h5>
          <p class="framer-text framer-styles-preset-1rzok9v">
           Check out the highlights below, and visit our
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/changelog" rel="noopener">
            changelog
           </a>
           <!--/$-->
           for a complete list of updates, improvements, and bug fixes.
          </p>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Salesforce integration - sync permissions field data from Ortto to Salesforce
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Salesforce activity sync - When syncing "Sent email" activity to Salesforce, include a 30-day link to view email as received by contact
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             New API method - "Get all campaigns"
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Journey notification email shape - use a merge tag for the recipient email
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Talk - More control for your inbox display preferences
            </p>
           </li>
          </ul>
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            New and noteworthy knowledge base articles:
           </strong>
          </h3>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-883-linkedin-ads-integration" rel="noopener" target="_blank">
              <strong class="framer-text">
               LinkedIn Ads integration
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
              - Manage LinkedIn lead gen form data in Ortto CDP,  sync conversion activities back to Linkedin
             </strong>
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-885-organizations-is-now-accounts" rel="noopener" target="_blank">
              <strong class="framer-text">
               Organizations is now âAccountsâ
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
              - Aligning to industry norms and preparing for a raft of new ABM features
             </strong>
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-887-using-the-api-to-export-campaign-data" rel="noopener" target="_blank">
              <strong class="framer-text">
               Using the API to export campaign data
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
              - Useful for exporting campaigns and their statuses, allowing you to audit your Ortto campaigns externally
             </strong>
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-682-email-suppression-list#Create-an-email-suppression-list" rel="noopener" target="_blank">
              <strong class="framer-text">
               Suppresion list config options
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
              - You can now choose to suppress from
             </strong>
             <em class="framer-text">
              <strong class="framer-text">
               transactional emails only
              </strong>
             </em>
             <strong class="framer-text">
              or
             </strong>
             <em class="framer-text">
              <strong class="framer-text">
               all emails
              </strong>
             </em>
             <strong class="framer-text">
              in the suppression list.
             </strong>
            </p>
           </li>
          </ul>
          <p class="framer-text framer-styles-preset-1rzok9v">
           <br class="framer-text trailing-break"/>
          </p>
         </div>
        </div>
        <div class="framer-10pwnmh" data-framer-name="Divider" name="Divider">
        </div>
       </div>
       <div class="framer-1q7sc0w">
        <div class="framer-1gomkm6" data-framer-name="Content" name="Content">
         <div class="framer-su41v0">
          <div class="framer-hvot7y" data-framer-component-type="RichTextContainer" data-framer-name="Date" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1rzok9v" data-styles-preset="Ydq3RYFlI" style="--framer-text-color:var(--token-e119c063-d39f-482e-9d29-7989ac59aa0e, rgb(109, 107, 112))">
            May 30, 2025
           </p>
          </div>
          <div class="framer-10qvk08" data-framer-component-type="RichTextContainer" data-framer-name="Title" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1y7h2ud" data-styles-preset="OFIy16ZPO">
            May Update
           </p>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 120px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png" srcset="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=512 512w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 48px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png" srcset="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=512 512w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 80px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png" srcset="https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=512 512w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/C1kZVyGVfEOwnBzGx9rXXvVM6zw.png 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="framer-11f1b56" data-framer-component-type="RichTextContainer" data-framer-name="Content" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            Always improving
           </strong>
          </h3>
          <h5 class="framer-text framer-styles-preset-t34kef">
           2025 is flying, and the Ortto product team are too ð. Our work in May brings new integrations and back-end performance improvements designed to increase productivity. Do you have feedback or suggestions? You can join the discussion
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/" rel="noopener" target="_blank">
            here
           </a>
           <!--/$-->
           (login with your Ortto account credentials).
          </h5>
          <p class="framer-text framer-styles-preset-1rzok9v">
           Check out the highlights below, and visit our
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/changelog" rel="noopener">
            changelog
           </a>
           <!--/$-->
           for a complete list of updates, improvements, and bug fixes.
          </p>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              LinkedIn integration (beta)
             </strong>
             - Contact us to join the beta program.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Export campaign metadata
             </strong>
             - Export single or multiple campaign data via CSV.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Send non-transactional emails via the API
             </strong>
             - Greater flexibility and increased efficiency through automation.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Log of past notifications sent added to Notification settings page
             </strong>
             - Bolstering our audit and governance capabilities.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              New campaign filter options: âhas not startedâ and âhas not leftâ
             </strong>
             - Good for long term (90+days) nurture campaigns.
            </p>
           </li>
          </ul>
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            New and noteworthy knowledge base articles:
           </strong>
          </h3>
          <h5 class="framer-text framer-styles-preset-t34kef">
           <strong class="framer-text">
            Omnichannel comms
           </strong>
          </h5>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-666-email-handoff" rel="noopener" target="_blank">
              Email handoff
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-827-send-emails-via-api" rel="noopener" target="_blank">
              Send emails via API
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-20-sms-limits-and-fallback" rel="noopener" target="_blank">
              SMS limits and fallback
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-800-whatsapp-integration" rel="noopener" target="_blank">
              Whatsapp integration
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-819-sending-whatsapp-messages" rel="noopener" target="_blank">
              Sending WhatsApp messages
             </a>
             <!--/$-->
            </p>
           </li>
          </ul>
          <h5 class="framer-text framer-styles-preset-t34kef">
           <strong class="framer-text">
            CDP
           </strong>
          </h5>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-846-aws-s3-integration" rel="noopener" target="_blank">
              Amazon S3 integration
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-702-supported-field-data-types" rel="noopener" target="_blank">
              Supported field data types
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/managing-audiences" rel="noopener" target="_blank">
              Managing audiences
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-745-salesforce-selective-sync" rel="noopener" target="_blank">
              Salesforce selective sync
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-858-backdating-custom-activities-via-zapier" rel="noopener" target="_blank">
              How to backdate custom activities via Zapier
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-882-what-is-the-apcid" rel="noopener" target="_blank">
              What is the APCID
             </a>
             <!--/$-->
            </p>
           </li>
          </ul>
          <h5 class="framer-text framer-styles-preset-t34kef">
           <br class="framer-text trailing-break"/>
          </h5>
          <p class="framer-text framer-styles-preset-1rzok9v">
           <br class="framer-text trailing-break"/>
          </p>
         </div>
        </div>
        <div class="framer-10pwnmh" data-framer-name="Divider" name="Divider">
        </div>
       </div>
       <div class="framer-1q7sc0w">
        <div class="framer-1gomkm6" data-framer-name="Content" name="Content">
         <div class="framer-su41v0">
          <div class="framer-hvot7y" data-framer-component-type="RichTextContainer" data-framer-name="Date" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1rzok9v" data-styles-preset="Ydq3RYFlI" style="--framer-text-color:var(--token-e119c063-d39f-482e-9d29-7989ac59aa0e, rgb(109, 107, 112))">
            April 23, 2025
           </p>
          </div>
          <div class="framer-10qvk08" data-framer-component-type="RichTextContainer" data-framer-name="Title" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1y7h2ud" data-styles-preset="OFIy16ZPO">
            April Update
           </p>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 120px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg" srcset="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 48px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg" srcset="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 80px, 720px)" decoding="async" height="756" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg" srcset="https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=512 512w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg?scale-down-to=1024 1024w,https://framerusercontent.com/images/NzsCoIm6uW4lqouixUbeH9Vw5Q.jpg 1440w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="1440"/>
           </div>
          </div>
         </div>
         <div class="framer-11f1b56" data-framer-component-type="RichTextContainer" data-framer-name="Content" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            You ask and we listen.
           </strong>
          </h3>
          <h5 class="framer-text framer-styles-preset-t34kef">
           Every month the Ortto product team reviews customer feedback and feature requests, and every month we make incremental improvements saving our customers hundreds of hours through new features and productivity improvements. You can join the discussion
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/" rel="noopener" target="_blank">
            here
           </a>
           <!--/$-->
           (login with your Ortto account credentials).
          </h5>
          <p class="framer-text framer-styles-preset-1rzok9v">
           Our most recent feature updates are focussed on delivering more control for your data for syncronising with third party systems, exporting for analysis and streamlining workflows. Check out the highlights below, and visit our
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/changelog" rel="noopener">
            changelog
           </a>
           <!--/$-->
           for a complete list of updates, improvements, and bug fixes.
          </p>
          <p class="framer-text framer-styles-preset-1rzok9v">
           <br class="framer-text trailing-break"/>
          </p>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Zapier activity connector
             </strong>
             - Supports backfilling CDP activities via Zapier - eliminating the need to build custom API integrations in some circumstances.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              AWS data sync
             </strong>
             (Beta). A robust way to sync data between Ortto CDP and AWS-S3 enabling batch syncing with third party services.
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-846-aws-s3-integration" rel="noopener">
              Learn more
             </a>
             <!--/$-->
             .
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Campaign Data Export
             </strong>
             - Export campaign metadata into CSV. Enables campaign data to be exported to CSV for analysis, enrichment or use in third party tools.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              AI Decision and AI Enrichment templates
             </strong>
             - Keep checking back as we add more AI templates based on most commonly used scenarios.
             <strong class="framer-text">
             </strong>
            </p>
           </li>
          </ul>
          <h3 class="framer-text framer-styles-preset-e3d07y">
           <strong class="framer-text">
            New and noteworthy knowledge base articles:
           </strong>
          </h3>
          <h5 class="framer-text framer-styles-preset-t34kef">
           <strong class="framer-text">
            CDP features
           </strong>
          </h5>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-847-using-api-to-send-transactional-push-notifications" rel="noopener">
              Send transactional push notifications via API
             </a>
             <!--/$-->
             - use the Ortto API to send SMS messages by using a dedicated transactional push notification API.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-862-adding-woocommerce-content-to-your-emails" rel="noopener">
              Adding WooCommerce content to your emails
             </a>
             <!--/$-->
             .
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Syncing
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-855-missing-salesforce-fields-or-field-values" rel="noopener">
              salesforce fields or field values
             </a>
             <!--/$-->
             .
            </p>
           </li>
          </ul>
          <h5 class="framer-text framer-styles-preset-t34kef">
           <strong class="framer-text">
            Email best practices:
           </strong>
          </h5>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-859-common-deliverability-issues-and-how-to-resolve-them" rel="noopener">
              Common deliverability issues and how to resolve them
             </a>
             <!--/$-->
             .
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Managing your
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-860-domain-reputation-issues" rel="noopener">
              domain reputation
             </a>
             <!--/$-->
             .
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             How
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-861-inconsistent-email-volume-spikes" rel="noopener">
              inconsistent email volume spikes
             </a>
             <!--/$-->
             affect domain reputation.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             How
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-863-restarting-email-campaigns-after-a-sending-gap" rel="noopener">
              restarting email campaigns after a sending gap
             </a>
             <!--/$-->
             can impact deliverability.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Dealing with
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-864-accidental-email-sends" rel="noopener">
              accidental email sends
             </a>
             <!--/$-->
             .
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Ramping up your
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-865-domain-ip-warming-issues" rel="noopener">
              domain and IP warming guidelines.
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             Implementing
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-866-dark-mode-design-guidelines-for-email" rel="noopener">
              dark mode -
             </a>
             <!--/$-->
             design guidelines.
            </p>
           </li>
          </ul>
         </div>
        </div>
        <div class="framer-10pwnmh" data-framer-name="Divider" name="Divider">
        </div>
       </div>
       <div class="framer-1q7sc0w">
        <div class="framer-1gomkm6" data-framer-name="Content" name="Content">
         <div class="framer-su41v0">
          <div class="framer-hvot7y" data-framer-component-type="RichTextContainer" data-framer-name="Date" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1rzok9v" data-styles-preset="Ydq3RYFlI" style="--framer-text-color:var(--token-e119c063-d39f-482e-9d29-7989ac59aa0e, rgb(109, 107, 112))">
            March 28, 2025
           </p>
          </div>
          <div class="framer-10qvk08" data-framer-component-type="RichTextContainer" data-framer-name="Title" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
           <p class="framer-text framer-styles-preset-1y7h2ud" data-styles-preset="OFIy16ZPO">
            March Update
           </p>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 120px, 720px)" decoding="async" height="1228" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png" srcset="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=512 512w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png 2048w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="2048"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 48px, 720px)" decoding="async" height="1228" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png" srcset="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=512 512w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png 2048w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="2048"/>
           </div>
          </div>
         </div>
         <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
          <div class="framer-n2gxnm" data-border="true" data-framer-name="Image" name="Image">
           <div data-framer-background-image-wrapper="true" style="position:absolute;border-radius:inherit;top:0;right:0;bottom:0;left:0">
            <img alt="" data-framer-original-sizes="min(100vw - 80px, 720px)" decoding="async" height="1228" loading="lazy" sizes="(min-width: 1400px) min(100vw - 120px, 720px), (min-width: 1024px) and (max-width: 1399px) min(100vw - 120px, 720px), (max-width: 809px) min(100vw - 48px, 720px), (min-width: 810px) and (max-width: 1023px) min(100vw - 80px, 720px)" src="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png" srcset="https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=512 512w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png?scale-down-to=1024 1024w,https://framerusercontent.com/images/1gwnbpUI2vN4wV50hPZcwx7GX0.png 2048w" style="display:block;width:100%;height:100%;border-radius:inherit;object-position:center;object-fit:cover" width="2048"/>
           </div>
          </div>
         </div>
         <div class="framer-11f1b56" data-framer-component-type="RichTextContainer" data-framer-name="Content" style="outline:none;display:flex;flex-direction:column;justify-content:flex-start;flex-shrink:0;transform:none">
          <p class="framer-text framer-styles-preset-1rzok9v">
           This month, we launched Ortto AI, our brand new AI-powered features, which included new capabilities for filters and journeys. Weâve also made several platform updates, bug fixes, and additions to our knowledge base.
          </p>
          <p class="framer-text framer-styles-preset-1rzok9v">
           See the highlights below, and visit our
           <!--$-->
           <a class="framer-text framer-styles-preset-1wicq5s" href="https://roadmap.ortto.com/changelog" rel="noopener">
            changelog
           </a>
           <!--/$-->
           for a complete list of updates, improvements, and bug fixes.
          </p>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              AI Filters â
             </strong>
             Provide a written prompt describing the contacts or organizations you are looking for, and the AI will create your filters for you, e.g. âContacts in Europeâ.
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-841-ai-filters" rel="noopener">
              Learn how to implement AI Filters
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              AI Decision Shapes â
             </strong>
             Describe the kind of users youâre looking for or the actions they should be taking, and the shape will generate conditions for your desired result, e.g. âIs this new contact likely to convert in the next 30 days?â
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-842-ai-decision-shape" rel="noopener">
              Learn how to implement AI Decision Shapes
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              AI Enrichment â
             </strong>
             Use your contactsâ data and activities to populate values for any text field via AI Enrichment. Enrich contacts, score leads, draft emails, messages, and more.
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-844-ai-enrichment" rel="noopener">
              Learn how to implement AI Enrichment
             </a>
             <!--/$-->
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Capture â
             </strong>
             Data from capture submissions like a userâs website or email address can now be validated, preventing fraudulent or malicious submissions
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              API â
             </strong>
             You can now enter a sample JSON payload when building an email and preview how the email will look with dynamic data.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <strong class="framer-text">
              Other â
             </strong>
             You can now build merge tags for whether a contact is or is not a member of an audience or email subscriber
            </p>
           </li>
          </ul>
          <p class="framer-text framer-styles-preset-1rzok9v">
           <strong class="framer-text">
            New and noteworthy knowledge base articles:
           </strong>
          </p>
          <ul class="framer-text">
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-841-ai-filters" rel="noopener">
              <strong class="framer-text">
               AI filters
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             -
             <strong class="framer-text">
             </strong>
             Learn how to use the new AI filter feature.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-844-ai-enrichment" rel="noopener">
              <strong class="framer-text">
               AI enrichment
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             -
             <strong class="framer-text">
             </strong>
             Learn how to use the new AI enrichment feature.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-842-ai-decision-shape" rel="noopener">
              <strong class="framer-text">
               AI decision shape
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             -
             <strong class="framer-text">
             </strong>
             Learn how to use the new AI decision shape.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-838-understanding-how-contacts-are-created" rel="noopener">
              <strong class="framer-text">
               Understanding how contacts are created
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             - Learn how contacts are created and what source theyâve originated from.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-836-managing-the-email-suppression-list-via-api" rel="noopener">
              <strong class="framer-text">
               Managing the email suppression list via API
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             - In this article, youâll find the relevant API endpoints to manage your email suppression list via API.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-840-benefits-of-custom-tracking-subdomain" rel="noopener">
              <strong class="framer-text">
               Benefits of custom tracking subdomain
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             - This article explains the benefits of using a custom tracking subdomain.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-848-how-to-add-a-countdown-timer-to-your-email" rel="noopener">
              <strong class="framer-text">
               How to add a countdown timer to your email
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
             </strong>
             - Learn how to add a countdown timer to your emails.
            </p>
           </li>
           <li class="framer-text framer-styles-preset-1rzok9v" data-preset-tag="p">
            <p class="framer-text framer-styles-preset-1rzok9v">
             <!--$-->
             <a class="framer-text framer-styles-preset-1wicq5s" href="https://help.ortto.com/a-849-adding-shopify-content-to-your-emails" rel="noopener">
              <strong class="framer-text">
               Adding Shopify content to your emails
              </strong>
             </a>
             <!--/$-->
             <strong class="framer-text">
              -
             </strong>
             Learn how to add Shopify content to your emails
            </p>
           </li>
          </ul>
         </div>
        </div>
        <div class="framer-10pwnmh" data-framer-name="Divider" name="Divider">
        </div>
       </div>
       <div class="ssr-variant hidden-13ggqhr hidden-29ubib">
        <div class="framer-1n0i5et-container">
         <div class="framer-8EaGQ framer-1hbo82m framer-v-1hbo82m" data-framer-name="Loading">
          <div class="framer-1qrc6yg" data-framer-name="Spinner" style="mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;-webkit-mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;will-change:transform;opacity:0;transform:none">
           <div class="framer-1jivlv8" data-framer-name="Conic" style="background:conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 0deg, rgb(153, 153, 153) 342deg);will-change:transform;opacity:1;transform:none">
            <div class="framer-m7b4s7" data-framer-name="Round" style="background-color:rgb(153, 153, 153);border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px">
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
       <div class="ssr-variant hidden-q4xlbk hidden-11j12rb hidden-29ubib">
        <div class="framer-1n0i5et-container">
         <div class="framer-8EaGQ framer-1hbo82m framer-v-1hbo82m" data-framer-name="Loading">
          <div class="framer-1qrc6yg" data-framer-name="Spinner" style="mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;-webkit-mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;will-change:transform;opacity:0;transform:none">
           <div class="framer-1jivlv8" data-framer-name="Conic" style="background:conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 0deg, rgb(153, 153, 153) 342deg);will-change:transform;opacity:1;transform:none">
            <div class="framer-m7b4s7" data-framer-name="Round" style="background-color:rgb(153, 153, 153);border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px">
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
       <div class="ssr-variant hidden-13ggqhr hidden-q4xlbk hidden-11j12rb">
        <div class="framer-1n0i5et-container">
         <div class="framer-8EaGQ framer-1hbo82m framer-v-1hbo82m" data-framer-name="Loading">
          <div class="framer-1qrc6yg" data-framer-name="Spinner" style="mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;-webkit-mask:url('https://framerusercontent.com/images/pGiXYozQ3mE4cilNOItfe2L2fUA.svg') alpha no-repeat center / cover add;will-change:transform;opacity:0;transform:none">
           <div class="framer-1jivlv8" data-framer-name="Conic" style="background:conic-gradient(from 0deg at 50% 50%, rgba(255, 255, 255, 0) 0deg, rgb(153, 153, 153) 342deg);will-change:transform;opacity:1;transform:none">
            <div class="framer-m7b4s7" data-framer-name="Round" style="background-color:rgb(153, 153, 153);border-bottom-left-radius:1px;border-bottom-right-radius:1px;border-top-left-radius:1px;border-top-right-radius:1px">
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
       <!--/$-->
      </div>
     </div>
    </div>
    <div class="framer-56xpzq-container">
     <div style="display:none;position:fixed;left:20px;right:92px;bottom:20px;max-width:448px;z-index:10;color:#252525;font-family:'Haas Grot Text R Web 55 Roman';font-size:14px;line-height:20px;letter-spacing:0.05px">
      <div style="min-height:40px;border:1px solid #e5e5e5;border-radius:8px;background:white;padding:12px 16px">
       ðª We use
       <!-- -->
       <a href="/cookies/" style="color:inherit;text-decoration-color:rgb(37 37 37 / 30%)">
        cookies
       </a>
       <!-- -->
       to improve your experience on our website. You can find out more in our
       <!-- -->
       <a href="/privacy/" style="color:inherit;text-decoration-color:rgb(37 37 37 / 30%)">
        policy
       </a>
       .
       <!-- -->
       <button style="color:#2458de;text-decoration:underline;cursor:pointer;font:inherit;appearance:none;background:none;border:none">
        Accept all cookies
       </button>
      </div>
     </div>
    </div>
   </div>
   <div class="framer-PUeXB framer-EvwNn framer-Q7j2r framer-nxPt4 framer-rTzsG framer-4GIj9 framer-d9Mk0 framer-phynS framer-vlcKs framer-j0Dxz framer-x99CK framer-n93qy framer-uunPM framer-2KVFG framer-YQsNO" id="overlay">
   </div>
   <!--/$-->
  </div>
  <script>
   (()=>{function u(){function n(t,e,i){let r=document.createElement("a");r.href=t,r.target=i,r.rel=e,document.body.appendChild(r),r.click(),r.remove()}function o(t){if(this.dataset.hydrated){this.removeEventListener("click",o);return}t.preventDefault(),t.stopPropagation();let e=this.getAttribute("href");if(!e)return;if(/Mac|iPod|iPhone|iPad/u.test(navigator.userAgent)?t.metaKey:t.ctrlKey)return n(e,"","_blank");let r=this.getAttribute("rel")??"",c=this.getAttribute("target")??"";n(e,r,c)}function a(t){if(this.dataset.hydrated){this.removeEventListener("auxclick",o);return}t.preventDefault(),t.stopPropagation();let e=this.getAttribute("href");e&&n(e,"","_blank")}function s(t){if(this.dataset.hydrated){this.removeEventListener("keydown",s);return}if(t.key!=="Enter")return;t.preventDefault(),t.stopPropagation();let e=this.getAttribute("href");if(!e)return;let i=this.getAttribute("rel")??"",r=this.getAttribute("target")??"";n(e,i,r)}document.querySelectorAll("[data-nested-link]").forEach(t=>{t instanceof HTMLElement&&(t.addEventListener("click",o),t.addEventListener("auxclick",a),t.addEventListener("keydown",s))})}return u})()()
  </script>
  <script>
   (()=>{function i(){for(let e of document.querySelectorAll("[data-framer-original-sizes]")){let t=e.getAttribute("data-framer-original-sizes");t===""?e.removeAttribute("sizes"):e.setAttribute("sizes",t),e.removeAttribute("data-framer-original-sizes")}}function a(){window.__framer_onRewriteBreakpoints=i}return a})()()
  </script>
  <script>
   !function(){function c(t,r){let e=r.indexOf("#"),n=e===-1?r:r.substring(0,e),o=e===-1?"":r.substring(e),a=n.indexOf("?");if(a===-1)return n+t+o;let d=new URLSearchParams(t),h=n.substring(a+1),s=new URLSearchParams(h);for(let[i,m]of d)s.has(i)||s.append(i,m);return n.substring(0,a+1)+s.toString()+o}var l='div#main a[href^="#"],div#main a[href^="/"],div#main a[href^="."]',u="div#main a[data-framer-preserve-params]",f,g=(f=document.currentScript)==null?void 0:f.hasAttribute("data-preserve-internal-params");if(window.location.search&&!/bot|-google|google-|yandex|ia_archiver|crawl|spider/iu.test(navigator.userAgent)){let t=document.querySelectorAll(g?`${l},${u}`:u);for(let r of t){let e=c(window.location.search,r.href);r.setAttribute("href",e)}}

}()
  </script>
  <script data-framer-appear-animation="reduce">
  </script>
  <script>
   (()=>{function d(e){let t=Date.prototype.toLocaleString,o=Date.prototype.toLocaleDateString;t&&(Date.prototype.toLocaleString=function(r,n){let i=s(this,r,n);return u(e.current.Date.toLocaleString,i,()=>t.call(this,r,n))}),o&&(Date.prototype.toLocaleDateString=function(r,n){let i=s(this,r,n);return u(e.current.Date.toLocaleDateString,i,()=>o.call(this,r,n))});let a=Object.getOwnPropertyDescriptors(Intl.DateTimeFormat.prototype).format.get,c=Intl.DateTimeFormat.prototype.formatRange,b=Intl.DateTimeFormat.prototype.formatToParts,D=Intl.DateTimeFormat.prototype.formatRangeToParts;a&&Object.defineProperty(Intl.DateTimeFormat.prototype,"format",{get(){function m(r){let n=p(this),i=s(r,n);return u(e.current.DateTimeFormat.format,i,()=>a.call(this)(r))}return m.bind(this)}}),c&&(Intl.DateTimeFormat.prototype.formatRange=function(r,n){let i=p(this),l=s(r,n,i);return u(e.current.DateTimeFormat.formatRange,l,()=>c.call(this,r,n))}),b&&(Intl.DateTimeFormat.prototype.formatToParts=function(r){let n=p(this),i=s(r,n);return u(e.current.DateTimeFormat.formatToParts,i,()=>b.call(this,r)).map(g)}),D&&(Intl.DateTimeFormat.prototype.formatRangeToParts=function(r,n){let i=p(this),l=s(r,n,i);return u(e.current.DateTimeFormat.formatRangeToParts,l,()=>D.call(this,r,n)).map(g)});let y=Number.prototype.toLocaleString;y&&(Number.prototype.toLocaleString=function(r,n){let i=s(this,r,n);return u(e.current.Number.toLocaleString,i,()=>y.call(this,r,n))});let h=Object.getOwnPropertyDescriptors(Intl.NumberFormat.prototype).format.get,F=Intl.NumberFormat.prototype.formatRange,T=Intl.NumberFormat.prototype.formatToParts,I=Intl.NumberFormat.prototype.formatRangeToParts;h&&Object.defineProperty(Intl.NumberFormat.prototype,"format",{get(){function m(r){let n=f(this),i=s(r,n);return u(e.current.NumberFormat.format,i,()=>h.call(this)(r))}return m.bind(this)}}),F&&(Intl.NumberFormat.prototype.formatRange=function(r,n){let i=f(this),l=s(r,n,i);return u(e.current.NumberFormat.formatRange,l,()=>F.call(this,r,n))}),T&&(Intl.NumberFormat.prototype.formatToParts=function(r){let n=f(this),i=s(r,n);return u(e.current.NumberFormat.formatToParts,i,()=>T.call(this,r)).map(g)}),I&&(Intl.NumberFormat.prototype.formatRangeToParts=function(r,n){let i=f(this),l=s(r,n,i);return u(e.current.NumberFormat.formatRangeToParts,l,()=>I.call(this,r,n)).map(g)})}function P(e,t){return typeof t=="bigint"?`${t}n`:t instanceof Date?t.getTime():t}function s(...e){let t=JSON.stringify(e,P),o=0;for(let a=0;a<t.length;a++)o+=t.charCodeAt(a),o+=o<<10,o^=o>>6;return o+=o<<3,o^=o>>11,o+=o<<15,o>>>0}function u(e,t,o){let a=e[t];if(typeof a<"u")return a;let c=o();return e[t]=c,c}function g(e){return{...e}}function p(e){let t=e.resolvedOptions(),o={locale:t.locale,calendar:t.calendar,numberingSystem:t.numberingSystem,timeZone:t.timeZone,hour12:t.hour12,weekday:t.weekday,era:t.era,year:t.year,month:t.month,day:t.day,hour:t.hour,minute:t.minute,second:t.second,timeZoneName:t.timeZoneName};for(let a in t)a in o||(o[a]=t[a]);return o}function f(e){let t=e.resolvedOptions(),o={locale:t.locale,numberingSystem:t.numberingSystem,style:t.style,currency:t.currency,currencyDisplay:t.currencyDisplay,currencySign:t.currencySign,unit:t.unit,unitDisplay:t.unitDisplay,minimumIntegerDigits:t.minimumIntegerDigits,minimumFractionDigits:t.minimumFractionDigits,maximumFractionDigits:t.maximumFractionDigits,minimumSignificantDigits:t.minimumSignificantDigits,maximumSignificantDigits:t.maximumSignificantDigits,useGrouping:t.useGrouping===!0?"auto":t.useGrouping,notation:t.notation,compactDisplay:t.compactDisplay,signDisplay:t.signDisplay,roundingIncrement:t.roundingIncrement??1,roundingMode:t.roundingMode??"halfExpand",roundingPriority:t.roundingPriority??"auto",trailingZeroDisplay:t.trailingZeroDisplay??"auto"};for(let a in t)a in o||(o[a]=t[a]);return o}return d})()({current:{"Date":{"toLocaleString":{"1019284083":"May 30, 2025","2810185642":"June 26, 2025","3946085275":"April 23, 2025","3954894467":"March 28, 2025"},"toLocaleDateString":{}},"DateTimeFormat":{"format":{},"formatRange":{},"formatToParts":{},"formatRangeToParts":{}},"Number":{"toLocaleString":{}},"NumberFormat":{"format":{},"formatRange":{},"formatToParts":{},"formatRangeToParts":{}}}})
  </script>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/react.B9m5TU7y.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/rolldown-runtime.CZ3jvRIN.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/framer.DLO-6V-G.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/motion.D2rq93Rh.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/sgWUlLDfs_4A-nlz9oCGvKOuFIz516LbNiiBNPsGdco.BUE3gLmj.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/Embed._cVr2xkk.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/shared.BI3WMhUm.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/n6hyeXo7w.xQjPHdCj.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/ro7OPezbn.nJiTp4RW.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/ZXkuuopsO.DtAPhsxM.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/MA0lYU1TT.BbcmLA2c.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/Uzja8vGl_.C25JWiAH.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/nBImoJ3iQ.C3js6Eig.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/Video_play_pause.-ZEltjzq.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/Ydq3RYFlI.Dlj1Pkua.mjs" rel="modulepreload"/>
  <link fetchpriority="low" href="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/script_main.KuOcpVDe.mjs" rel="modulepreload"/>
  <script async="" data-framer-bundle="main" fetchpriority="low" src="https://framerusercontent.com/sites/qJg9mD43nZnIosQmO5o7v/script_main.KuOcpVDe.mjs" type="module">
  </script>
  <div aria-hidden="true" id="svg-templates" style="position: absolute; overflow: hidden; bottom: 0; left: 0; width: 0; height: 0; z-index: 0; contain: strict">
   <svg fill="none" height="36" id="svg-107607693_742" width="57">
    <path d="M10.719 18.53a3.712 3.712 0 1 1-7.423.074 3.712 3.712 0 0 1 7.423-.074Zm3.295 0a7.008 7.008 0 1 0-7.013 6.983 7.007 7.007 0 0 0 7.007-7.001l.006.018Zm4.606-3.693h4.55V11.64h-2.755a5.164 5.164 0 0 0-5.409 5.408v8.33h3.4V15.1a.22.22 0 0 1 .22-.22l-.006-.043ZM38.753 25.42h2.756v-3.216h-4.545a.22.22 0 0 1-.22-.22V14.86h4.765v-3.197h-4.771V8.24h-3.4v3.424h-5.727V8.24h-3.399V20a5.158 5.158 0 0 0 5.402 5.409h2.763v-3.204h-4.551a.22.22 0 0 1-.22-.22V14.86h5.732V20a5.157 5.157 0 0 0 5.409 5.408l.006.012Zm13.971-6.885a3.706 3.706 0 1 1-7.412.061 3.706 3.706 0 0 1 7.412-.06Zm3.295 0a7 7 0 1 0-6.994 6.977A7.006 7.006 0 0 0 56 18.53l.018.006Z" fill="#fff">
    </path>
   </svg>
  </div>
  <!-- Start of bodyEnd -->
  <!-- End of bodyEnd -->
 </body>
</html>
